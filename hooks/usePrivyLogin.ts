import { useIdentityToken, usePrivy, useWallets } from "@privy-io/react-auth";
import { useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useCurrentAccount, useDisconnectWallet } from "@mysten/dapp-kit";
import { AppDispatch, RootState } from "@/store";
import { setUserAuth } from "@/store/user.store";
import Storage from "@/libs/storage";
import { LOGIN_METHODS, NETWORKS } from "@/utils/contants";
import { closeSocketInstance, createSocketInstance } from "@/libs/socket";
import rf from "@/services/RequestFactory";
import { useSessionSigners } from "@privy-io/react-auth";
import config from "@/config";

export const usePrivyLogin = () => {
  const { login, authenticated, user: privyUser, logout } = usePrivy();
  const { identityToken } = useIdentityToken();
  const { ready: walletsReady } = useWallets();
  const dispatch = useDispatch<AppDispatch>();
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const { addSessionSigners, removeSessionSigners } = useSessionSigners();

  const exchangePrivyForJWT = useCallback(async (idToken: string) => {
    try {
      const response = await rf.getRequest("PrivyRequest").login(idToken);
      return response.jwtToken;
    } catch (error) {
      console.error("Failed to exchange Privy token for JWT:", error);
      throw error;
    }
  }, []);

  const handlePrivyAuthSuccess = useCallback(async () => {
    if (!identityToken) {
      console.error("No identity token available");
      return;
    }

    try {
      const jwtToken = await exchangePrivyForJWT(identityToken);

      dispatch(setUserAuth({ accessToken: jwtToken }));
      Storage.setLoginMethod(LOGIN_METHODS.PRIVY);

      const redirectAfterLogin = Storage.getRedirectAfterLogin();

      if (redirectAfterLogin) {
        const location = `${window.location.pathname}${window.location.search}`;
        if (location !== redirectAfterLogin) {
          Storage.clearRedirectAfterLogin();
          window.location.href = redirectAfterLogin;
        }
      } else {
        window.location.href = "/new-pairs";
      }

      closeSocketInstance(NETWORKS.SUI);
      createSocketInstance(NETWORKS.SUI, jwtToken);

      console.log("Privy authentication successful");
    } catch (error) {
      console.error("Privy authentication failed:", error);
    }
  }, [identityToken, exchangePrivyForJWT, dispatch]);

  useEffect(() => {
    if (identityToken && !accessToken) {
      handlePrivyAuthSuccess();
    }
  }, [identityToken, accessToken]);

  const onPrivyLogin = useCallback(() => {
    if (!authenticated) {
      login();
    } else {
      logout();
    }
  }, [login, authenticated]);

  const handlerAddSignerSession = useCallback(
    async (address: string) => {
      try {
        if (!walletsReady) {
          console.warn(
            "Wallet proxy not ready yet, skipping session signer setup"
          );
          return;
        }

        await addSessionSigners({
          signers: [
            {
              signerId: config.privyConfig.signerId,
            },
          ],
          address,
        });
      } catch (error) {
        console.error("Failed to add Privy signer:", error);
      }
    },
    [walletsReady, addSessionSigners]
  );

  const handlerRemoveSignerSession = useCallback(
    async (address: string) => {
      try {
        await removeSessionSigners({
          address,
        });
      } catch (error) {
        console.error("Failed to remove Privy signer:", error);
      }
    },
    [removeSessionSigners]
  );

  const createPrivyWallet = useCallback(async (numberWallets: number) => {
    try {
      const response = await rf
        .getRequest("PrivyRequest")
        .createWallet(numberWallets);

      return response;
    } catch (error) {
      console.error("Failed to create Privy wallet:", error);
      throw error;
    }
  }, []);

  return {
    onPrivyLogin,
    authenticated,
    user: privyUser,
    identityToken,
    handlerAddSignerSession,
    createPrivyWallet,
    handlerRemoveSignerSession,
  };
};
