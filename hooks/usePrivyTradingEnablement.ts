import { useCallback, useState } from "react";
import { usePrivy } from "@privy-io/react-auth";
import { usePrivyLogin } from "./usePrivyLogin";
import { toastError, toastSuccess } from "@/libs/toast";
import Storage from "@/libs/storage";
import { LOGIN_METHODS } from "@/utils/contants";

export const usePrivyTradingEnablement = () => {
  const { user: privyUser } = usePrivy();
  const { handlerAddSignerSession, handlerRemoveSignerSession } =
    usePrivyLogin();
  const [isEnabling, setIsEnabling] = useState(false);
  const [isDisabling, setIsDisabling] = useState(false);

  const isPrivyUser = Storage.getLoginMethod() === LOGIN_METHODS.PRIVY;

  const getAllSuiWallets = useCallback(() => {
    return (
      (privyUser?.linkedAccounts?.filter(
        (account: any) =>
          account.type === "wallet" && account.chainType === "sui"
      ) as any[]) || []
    );
  }, [privyUser?.linkedAccounts]);

  const suiWallet = getAllSuiWallets()[0];

  const isTradingEnabledForWallet = useCallback(
    (walletAddress: string) => {
      const wallet = getAllSuiWallets().find(
        (w: any) => w.address === walletAddress
      );
      return wallet?.delegated || false;
    },
    [getAllSuiWallets]
  );

  const isTradingEnabled = suiWallet?.delegated || false;
  const hasPrivyWallet = getAllSuiWallets().length > 0;

  const enableTradingForWallet = useCallback(
    async (walletAddress: string) => {
      if (!isPrivyUser) {
        toastError(
          "Error",
          "Trading enablement is only available for Privy users"
        );
        return false;
      }

      const wallet = getAllSuiWallets().find(
        (w: any) => w.address === walletAddress
      );
      if (!wallet) {
        toastError(
          "Error",
          "Wallet not found. Please check the wallet address."
        );
        return false;
      }

      if (wallet.delegated) {
        toastSuccess("Info", "Trading is already enabled for this wallet");
        return true;
      }

      setIsEnabling(true);
      try {
        await handlerAddSignerSession(walletAddress);
        toastSuccess("Success", "Trading has been enabled successfully!");
        return true;
      } catch (error) {
        console.error("Failed to enable trading:", error);
        toastError("Error", "Failed to enable trading. Please try again.");
        return false;
      } finally {
        setIsEnabling(false);
      }
    },
    [isPrivyUser, getAllSuiWallets, handlerAddSignerSession]
  );

  const disableTradingForWallet = useCallback(
    async (walletAddress: string) => {
      const wallet = getAllSuiWallets().find(
        (w: any) => w.address === walletAddress
      );
      if (!wallet) {
        toastError(
          "Error",
          "Wallet not found. Please check the wallet address."
        );
        return false;
      }

      if (!wallet.delegated) {
        toastSuccess("Info", "Trading is already disabled for this wallet");
        return true;
      }

      setIsDisabling(true);
      try {
        await handlerRemoveSignerSession(walletAddress);
        toastSuccess("Success", "Trading has been disabled successfully!");
        return true;
      } catch (error) {
        console.error("Failed to disable trading:", error);
        toastError("Error", "Failed to disable trading. Please try again.");
        return false;
      } finally {
        setIsDisabling(false);
      }
    },
    [isPrivyUser, getAllSuiWallets, handlerRemoveSignerSession]
  );

  const enableTrading = useCallback(
    async (walletAddress?: string) => {
      const targetAddress = walletAddress || suiWallet?.address;

      if (!targetAddress) {
        toastError(
          "Error",
          "No Privy wallet found. Please create a wallet first."
        );
        return false;
      }

      return enableTradingForWallet(targetAddress);
    },
    [suiWallet?.address, enableTradingForWallet]
  );

  const isProcessing = isEnabling || isDisabling;

  return {
    isPrivyUser,
    isTradingEnabled,
    hasPrivyWallet,
    isEnabling,
    enableTrading,
    getAllSuiWallets,
    isTradingEnabledForWallet,
    enableTradingForWallet,
    disableTradingForWallet,
    isDisabling,
    isProcessing,
  };
};
