import BigNumber from "bignumber.js";
import BaseSimulate from "../BaseSimulate";
import { TCoinMetadata, TPosition } from "@/types";
import { coinWithBalance, Transaction } from "@mysten/sui/transactions";
import {
  getSuiObject,
  getOwnerCoinOnchain,
  getReferenceGasPrice,
} from "@/utils/suiClient";
import { NETWORKS, SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import { CoinStruct } from "@mysten/sui/client";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import { isZero } from "@/utils/helper";
import rf from "@/services/RequestFactory";

export const STEAMM_PACKAGE =
  "0x4dfaa01e9fcc00ab2cd4f4b4dbb46ec502dfaf882ad1d8aa26d973949df41c7c";
export const STEAMM_LENDING_MARKET_TYPE =
  "0xf95b06141ed4a174f239417323bde3f209b972f5930d8521ea38a52aff3a6ddf::suilend::MAIN_POOL";
export const STEAMM_LENDING_MARKET_ID =
  "0x84030d26d85eaa7035084a057f2f11f701b7e2e4eda87551becbc7c97505ece1";

export default class SteammSimulate extends BaseSimulate {
  public name = "SteammSimulate";
  /**
   * Helper method to create and configure a base transaction
   */
  private createBaseTransaction = (
    walletAddress: string,
    gasBasePrice: bigint
  ): Transaction => {
    const tx = new Transaction();
    tx.setGasBudget(30000000);
    tx.setSender(
      "0x0000000000000000000000000000000000000000000000000000000000000000"
    );
    tx.setGasPrice(gasBasePrice);
    return tx;
  };

  private extractPoolTypeInfo = async (poolId: string) => {
    const poolObject = await getSuiObject({
      id: poolId,
      options: {
        showType: true,
      },
    });
    const poolType = poolObject.data?.type || "";

    const typeMatch = poolType.match(/Pool<([^>]+)>/);
    if (!typeMatch) throw new Error("Invalid pool type");
    const typeArgs = typeMatch[1].split(", ");

    return {
      bcoinAType: typeArgs[0],
      bcoinBType: typeArgs[1],
      lpTokenType: typeArgs[3],
    };
  };

  private getCoinTypes = (
    coinQuote: TCoinMetadata,
    coinBase: TCoinMetadata,
    isXQuoteToken: boolean
  ) => {
    return {
      coinAType: isXQuoteToken ? coinQuote.address : coinBase.address,
      coinBType: isXQuoteToken ? coinBase.address : coinQuote.address,
    };
  };

  private createSteammMoveCall = (
    tx: Transaction,
    target: string,
    poolId: string,
    feeTier: any,
    coinA: any,
    coinB: any,
    isXQuoteToken: boolean,
    typeInfo: { bcoinAType: string; bcoinBType: string; lpTokenType: string },
    coinTypes: { coinAType: string; coinBType: string }
  ) => {
    tx.moveCall({
      target,
      typeArguments: [
        STEAMM_LENDING_MARKET_TYPE,
        coinTypes.coinAType,
        coinTypes.coinBType,
        typeInfo.bcoinAType,
        typeInfo.bcoinBType,
        typeInfo.lpTokenType,
      ],
      arguments: [
        tx.object(poolId),
        tx.object(feeTier.bank_a_object_id),
        tx.object(feeTier.bank_b_object_id),
        tx.object(STEAMM_LENDING_MARKET_ID),
        coinA,
        coinB,
        tx.object("0x6"),
        tx.pure.u64("0"),
        tx.pure.bool(isXQuoteToken),
        tx.pure.string("-1"), // order id
      ],
    });
  };

  private buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    poolId: string,
    coinQuote: TCoinMetadata,
    coinBase: TCoinMetadata,
    isXQuoteToken: boolean,
    feeTier: any,
    gasBasePrice: bigint
  ) => {
    const tx = this.createBaseTransaction(walletAddress, gasBasePrice);
    const typeInfo = await this.extractPoolTypeInfo(poolId);
    const coinTypes = this.getCoinTypes(coinQuote, coinBase, isXQuoteToken);

    let coinA, coinB;

    if (coinTypes.coinAType == coinQuote?.address) {
      coinA = coinWithBalance({
        balance: BigInt(exactAmountIn.toString()),
        type: coinTypes.coinAType,
        useGasCoin: coinTypes.coinAType === SUI_TOKEN_ADDRESS_FULL,
      });

      coinB = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [coinTypes.coinBType],
      });
    } else {
      coinA = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [coinTypes.coinAType],
      });
      coinB = coinWithBalance({
        balance: BigInt(exactAmountIn.toString()),
        type: coinTypes.coinBType,
        useGasCoin: coinTypes.coinBType === SUI_TOKEN_ADDRESS_FULL,
      });
    }

    this.createSteammMoveCall(
      tx,
      `${STEAMM_PACKAGE}::steamm_cpmm_router::buy_exact_in`,
      poolId,
      feeTier,
      coinA,
      coinB,
      isXQuoteToken,
      typeInfo,
      coinTypes
    );

    return { tx, amountOut: NaN };
  };

  private buildSellTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    poolId: string,
    coinQuote: TCoinMetadata,
    coinBase: TCoinMetadata,
    feeTier: any,
    gasBasePrice: bigint,
    isXQuoteToken: boolean
  ) => {
    const tx = this.createBaseTransaction(walletAddress, gasBasePrice);

    const typeInfo = await this.extractPoolTypeInfo(poolId);
    const coinTypes = this.getCoinTypes(coinQuote, coinBase, isXQuoteToken);

    let coinA, coinB;
    tx.setSender(walletAddress);
    if (coinTypes.coinAType == coinBase?.address) {
      coinA = coinWithBalance({
        balance: BigInt(exactAmountIn.toString()),
        type: coinTypes.coinAType,
        useGasCoin: coinTypes.coinAType === SUI_TOKEN_ADDRESS_FULL,
      });

      coinB = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [coinTypes.coinBType],
      });
    } else {
      coinA = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [coinTypes.coinAType],
      });
      coinB = coinWithBalance({
        balance: BigInt(exactAmountIn.toString()),
        type: coinTypes.coinBType,
        useGasCoin: coinTypes.coinBType === SUI_TOKEN_ADDRESS_FULL,
      });
    }

    this.createSteammMoveCall(
      tx,
      `${STEAMM_PACKAGE}::steamm_cpmm_router::sell_exact_in`,
      poolId,
      feeTier,
      coinA,
      coinB,
      isXQuoteToken,
      typeInfo,
      coinTypes
    );

    return { tx, amountOut: NaN };
  };

  /**
   * Helper method to validate feeTier data
   */
  private validateFeeTier = (feeTier: any) => {
    if (!feeTier || !feeTier.bank_a_object_id || !feeTier.bank_b_object_id) {
      throw new Error(
        "Fee tier data with bank_a_object_id and bank_b_object_id is required"
      );
    }
  };

  /**
   * Helper method to get user coin objects and calculate sell amount
   */
  private prepareSellTransaction = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    sellPercent: number
  ) => {
    // Get user's coin objects for the token they want to sell
    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    // Calculate the exact amount to sell based on the percentage
    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return { coinObjs, exactAmountIn };
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata, // This is coinQuote (what we're spending)
    tokenOut: TCoinMetadata, // This is coinBase (what we're getting)
    isXQuoteToken: boolean,
    feeTier: any,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    console.log("starting simulate buy transaction", {
      walletAddress,
      amountIn,
      poolObjectId,
      tokenIn,
      tokenOut,
      isXQuoteToken,
      feeTier,
      gasBasePrice,
    });

    return this.buildBuyTransaction(
      walletAddress,
      amountIn,
      poolObjectId,
      tokenIn, // coinQuote
      tokenOut, // coinBase
      isXQuoteToken,
      feeTier,
      gasBasePrice
    );
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint,
    feeTier: any,
    isXQuoteToken: boolean
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    if (!feeTier) {
      throw new Error("feeTier is required for Steamm transactions");
    }

    this.validateFeeTier(feeTier);

    const { exactAmountIn } = await this.prepareSellTransaction(
      walletAddress,
      tokenIn,
      sellPercent
    );
    console.log(exactAmountIn.toString(), "exactAmountIn");

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      poolObjectId,
      tokenOut, // coinQuote (what we're getting)
      tokenIn, // coinBase (what we're selling)
      feeTier,
      gasBasePrice,
      isXQuoteToken
    );
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const pair = await rf
      .getRequest("PairRequest")
      .getPair(NETWORKS.SUI, position.pair);

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const result = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.poolId,
      position.tokenQuote,
      position.tokenBase,
      JSON.parse(pair.feeTier),
      gasBasePrice,
      pair.isXQuoteToken
    );
    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };
}
