import { CoinStruct } from "@mysten/sui/client";
import { Transaction } from "@mysten/sui/transactions";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import BigNumber from "bignumber.js";
import { TCoinMetadata, TPosition } from "@/types";
import {
  SUI_TOKEN_ADDRESS_FULL,
  SUI_TOKEN_ADDRESS_SHORT,
  SUI_TOKEN_METADATA,
} from "@/utils/contants";
import { isZero } from "@/utils/helper";
import {
  getOwnerCoinOnchain,
  getReferenceGasPrice,
  getSuiObject,
} from "@/utils/suiClient";
import BaseSimulate from "../BaseSimulate";

const BLUEFIN_PACKAGE =
  "0x8752d4186f729d48696b4f3861c07506311f2768702f379420a148fa47b19b19";
const BLUEFIN_MODULE = "bluefin_router";
const BLUEFIN_CONFIG_OBJECT_ID =
  "0x03db251ba509a8d5d8777b6338836082335d93eecbdd09a11e190a1cff51c352";

export default class BluefinSimulate extends BaseSimulate {
  public name = "BluefinSimulate";
  private buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    gasBasePrice: bigint,
    poolObjectId: string,
    tokenIn: TCoinMetadata = SUI_TOKEN_METADATA
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    let coinTokenIn = null;

    if (
      tokenIn.address === SUI_TOKEN_ADDRESS_SHORT ||
      tokenIn.address === SUI_TOKEN_ADDRESS_FULL
    ) {
      const [coin] = tx.splitCoins(tx.gas, [
        tx.pure.u64(exactAmountIn.toString()),
      ]);
      coinTokenIn = coin;
    } else {
      const [tokenInObjects] = await getOwnerCoinOnchain(
        walletAddress,
        tokenIn.address
      );
      if (tokenInObjects.length > 1) {
        tx.mergeCoins(
          tokenInObjects[0].coinObjectId,
          tokenInObjects.slice(1).map((coin) => coin.coinObjectId)
        );
      }
      coinTokenIn = tx.object(tokenInObjects[0].coinObjectId);
    }

    const { data: poolObject } = await getSuiObject({
      id: poolObjectId,
      options: {
        showContent: true,
      },
    });
    const poolType = (poolObject?.content as any)?.type;
    const { tokenXAddress, tokenYAddress } =
      this.extractTokenX2YFromPoolType(poolType);
    if (!tokenXAddress || !tokenYAddress) {
      throw new Error("Invalid pool type");
    }
    const xToY =
      tokenXAddress &&
      normalizeStructTag(tokenXAddress) === normalizeStructTag(tokenIn.address);

    let tokenXObject = null;
    let tokenYObject = null;

    if (
      normalizeStructTag(tokenXAddress) === normalizeStructTag(tokenIn.address)
    ) {
      tokenXObject = coinTokenIn;
      tokenYObject = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [tokenYAddress],
      });
    } else {
      tokenXObject = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [tokenXAddress],
      });
      tokenYObject = coinTokenIn;
    }
    tx.moveCall({
      target: `${BLUEFIN_PACKAGE}::${BLUEFIN_MODULE}::buy_exact_in`,
      typeArguments: [tokenXAddress, tokenYAddress],
      arguments: [
        tx.object(BLUEFIN_CONFIG_OBJECT_ID),
        tx.object(poolObjectId),
        tokenXObject,
        tokenYObject,
        tx.pure.u64(0),
        tx.pure.u128(
          xToY ? BigInt("4295048017") : BigInt("79226673515401279992447579054")
        ),
        tx.object("0x6"),
        tx.pure.bool(xToY ? true : false), // buy = false, sell = true
        tx.pure.string("abc"),
      ],
    });

    return { tx, amountOut: NaN };
  };

  private buildSellTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber,
    tokenIn: TCoinMetadata,
    gasBasePrice: bigint,
    coinObjs: (CoinStruct & { owner: string })[],
    poolObjectId: string
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    if (coinObjs.length > 1) {
      tx.mergeCoins(
        coinObjs[0].coinObjectId,
        coinObjs.slice(1).map((coin) => coin.coinObjectId)
      );
    }

    const { data: poolObject } = await getSuiObject({
      id: poolObjectId,
      options: {
        showContent: true,
      },
    });
    const poolType = (poolObject?.content as any)?.type;
    const { tokenXAddress, tokenYAddress } =
      this.extractTokenX2YFromPoolType(poolType);
    if (!tokenXAddress || !tokenYAddress) {
      throw new Error("Invalid pool type");
    }
    const xToY =
      tokenXAddress &&
      normalizeStructTag(tokenXAddress) === normalizeStructTag(tokenIn.address);

    let tokenXObject = null;
    let tokenYObject = null;

    if (
      normalizeStructTag(tokenXAddress) === normalizeStructTag(tokenIn.address)
    ) {
      tokenXObject = tx.object(coinObjs[0].coinObjectId);
      tokenYObject = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [tokenYAddress],
      });
    } else {
      tokenXObject = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [tokenXAddress],
      });
      tokenYObject = tx.object(coinObjs[0].coinObjectId);
    }

    tx.moveCall({
      target: `${BLUEFIN_PACKAGE}::${BLUEFIN_MODULE}::sell_exact_in`, // package
      typeArguments: [tokenXAddress, tokenYAddress],
      arguments: [
        tx.object(
          BLUEFIN_CONFIG_OBJECT_ID // dex config cetus
        ),
        tx.object(
          poolObjectId // pool address
        ),
        tokenXObject,
        tokenYObject,
        tx.pure.u64(exactAmountIn.toString()), // amountIn
        tx.pure.u64(0), // amountOutMin
        tx.pure.u128(
          xToY ? BigInt("4295048017") : BigInt("79226673515401279992447579054")
        ), // hardcode
        tx.object("0x6"), // clock
        tx.pure.bool(xToY ? true : false), // buy = false, sell = true
        tx.pure.string("abc"), // orderId
      ],
    });
    return { tx, amountOut: NaN };
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata = SUI_TOKEN_METADATA,
    tokenOut: TCoinMetadata,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    return this.buildBuyTransaction(
      walletAddress,
      amountIn,
      gasBasePrice,
      poolObjectId,
      tokenIn
    );
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata = SUI_TOKEN_METADATA,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      tokenIn,
      gasBasePrice,
      coinObjs,
      poolObjectId
    );
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const result = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.token,
      gasBasePrice,
      tokenCoinObjects,
      position.poolId
    );
    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };
}
