import BigNumber from "bignumber.js";
import { Transaction } from "@mysten/sui/transactions";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import { isZero } from "@/utils/helper";
import { getOwnerCoinOnchain, getReferenceGasPrice } from "@/utils/suiClient";
import { TCoinMetadata, TPosition } from "@/types";
import { CoinStruct } from "@mysten/sui/client";
import BaseSimulate from "../BaseSimulate";

const BLUEMOVE_PACKAGE =
  "0x496d3ea87e24d055328c329e65d433c0ee2254c9154dbf66c6f95af578ae6353";
const BLUEMOVE_MODULE = "bluemove_router";
const BLUEMOVE_FEE_OBJECT_ID =
  "0x0e3a32590abb7ca1d2877898a3a7a3ff5dc68679e9dd7672c43e6bfdb0b9d9a8";
const BLUEMOVE_CONFIG_OBJECT_ID =
  "0x3f2d9f724f4a1ce5e71676448dc452be9a6243dac9c5b975a588c8c867066e92";

export default class BluemoveSimulate extends BaseSimulate {
  public name = "BluemoveSimulate";
  private buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
    gasBasePrice: bigint
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);
    const [coin] = tx.splitCoins(tx.gas, [
      tx.pure.u64(exactAmountIn.toString()),
    ]);

    tx.moveCall({
      target: `${BLUEMOVE_PACKAGE}::${BLUEMOVE_MODULE}::buy_exact_in`, // package
      typeArguments: [tokenOut.address],
      arguments: [
        tx.object(BLUEMOVE_FEE_OBJECT_ID),
        tx.pure.u64(exactAmountIn.toString()),
        coin,
        tx.pure.u64(0),
        tx.object(BLUEMOVE_CONFIG_OBJECT_ID),
        tx.pure.string("abc"),
      ],
    });
    return { tx, amountOut: NaN };
  };

  private buildSellTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber,
    tokenIn: TCoinMetadata,
    gasBasePrice: bigint,
    coinObjs: (CoinStruct & { owner: string })[]
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    if (coinObjs.length > 1) {
      tx.mergeCoins(
        coinObjs[0].coinObjectId,
        coinObjs.slice(1).map((coin) => coin.coinObjectId)
      );
    }

    tx.moveCall({
      target: `${BLUEMOVE_PACKAGE}::${BLUEMOVE_MODULE}::sell_exact_in`, // package
      typeArguments: [tokenIn.address],
      arguments: [
        tx.object(BLUEMOVE_FEE_OBJECT_ID),
        tx.pure.u64(exactAmountIn.toString()),
        tx.object(coinObjs[0].coinObjectId),
        tx.pure.u64(0),
        tx.object(BLUEMOVE_CONFIG_OBJECT_ID),
        tx.pure.string("abc"),
      ],
    });
    return { tx, amountOut: NaN };
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    return this.buildBuyTransaction(
      walletAddress,
      amountIn,
      tokenOut,
      gasBasePrice
    );
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      tokenIn,
      gasBasePrice,
      coinObjs
    );
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        position.token.address &&
        coin.coinType &&
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const result = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.token,
      gasBasePrice,
      tokenCoinObjects
    );
    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };
}
