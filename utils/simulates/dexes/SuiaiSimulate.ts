import { CoinStruct } from "@mysten/sui/client";
import { Transaction } from "@mysten/sui/transactions";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import BigNumber from "bignumber.js";
import { TCoinMetadata, TPosition } from "@/types";
import {
  SUI_TOKEN_ADDRESS_FULL,
  SUI_TOKEN_ADDRESS_SHORT,
  SUI_TOKEN_METADATA,
} from "@/utils/contants";
import { isZero } from "@/utils/helper";
import { getOwnerCoinOnchain, getReferenceGasPrice } from "@/utils/suiClient";

import BaseSimulate from "../BaseSimulate";
import { CETUS_CONFIG_OBJECT_ID } from "./CetusSimulate";

const PACKAGE_CETUS =
  "0x13a090e2ce489162ee140e3763e3d262897e98683c0bd2626b4dc8786319139b";
const PACKAGE_SUIAI_ROUTER =
  "0x4bbfd1c0ee1ef98648abbd8a046315ff72de39a55eba39c0635f90df37603cb2";
const POOL_SUIAI_SUI_OBJECT_ID =
  "0x7852612f5bf73613021f17353985fc186b3b224139c6a2576239132ba5a33b66";

const SUIAI_PACKAGE =
  "0x178f36c8760f0b6f92d2f91b26544ca16765979df874e077b2bf4e3f92a75359";
const SUIAI_MODULE = "suiai_router";
const SUIAI_CONFIG_OBJECT_ID =
  "0xd9b810f0d1f4c024dd7190bac834de764cb09054246f86981cb63d36ae51bf5c";

export default class SuiaiSimulate extends BaseSimulate {
  public name = "SuiaiSimulate";

  private buildBuyBySuiToken = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
    gasBasePrice: bigint,
    poolObjectId: string,
    tokenIn: TCoinMetadata = SUI_TOKEN_METADATA
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    const [coin] = tx.splitCoins(tx.gas, [
      tx.pure.u64(exactAmountIn.toString()),
    ]);

    const token = tx.moveCall({
      target: `${PACKAGE_CETUS}::cetus_router::buy_exact_in_second_return`,
      typeArguments: [tokenIn.address],
      arguments: [
        tx.object(CETUS_CONFIG_OBJECT_ID),
        tx.object(POOL_SUIAI_SUI_OBJECT_ID),
        coin,
        tx.pure.u64(exactAmountIn.toString()),
        tx.pure.u64(0),
        tx.object("0x6"),
        tx.pure.string("abc"),
      ],
    });

    tx.moveCall({
      target: `${PACKAGE_SUIAI_ROUTER}::suiai_router::buy_exact_in_v2`,
      typeArguments: [tokenOut.address],
      arguments: [
        tx.object(SUIAI_CONFIG_OBJECT_ID),
        tx.object(poolObjectId),
        token,
        tx.pure.u64(0),
        tx.pure.string("abc"),
      ],
    });

    return { tx, amountOut: NaN };
  };

  private buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
    gasBasePrice: bigint,
    poolObjectId: string,
    tokenIn: TCoinMetadata = SUI_TOKEN_METADATA,
    isBuyBySuiToken = false
  ) => {
    if (isBuyBySuiToken) {
      return await this.buildBuyBySuiToken(
        walletAddress,
        exactAmountIn,
        tokenOut,
        gasBasePrice,
        poolObjectId,
        tokenIn
      );
    }

    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    let coinTokenIn = null;

    let isPairWithSui = false;
    if (
      tokenIn.address === SUI_TOKEN_ADDRESS_SHORT ||
      tokenIn.address === SUI_TOKEN_ADDRESS_FULL
    ) {
      const [coin] = tx.splitCoins(tx.gas, [
        tx.pure.u64(exactAmountIn.toString()),
      ]);
      coinTokenIn = coin;
      isPairWithSui = true;
    } else {
      const [tokenInObjects] = await getOwnerCoinOnchain(
        walletAddress,
        tokenIn.address
      );
      if (tokenInObjects.length > 1) {
        tx.mergeCoins(
          tokenInObjects[0].coinObjectId,
          tokenInObjects.slice(1).map((coin) => coin.coinObjectId)
        );
      }
      coinTokenIn = tx.object(tokenInObjects[0].coinObjectId);
    }

    if (isPairWithSui) {
      tx.moveCall({
        target: `${SUIAI_PACKAGE}::${SUIAI_MODULE}::buy_exact_in_v1`,
        typeArguments: [tokenOut.address],
        arguments: [
          tx.object(SUIAI_CONFIG_OBJECT_ID),
          tx.object(poolObjectId),
          coinTokenIn,
          tx.pure.u64(exactAmountIn.toString()),
          tx.pure.u64(0),
          tx.pure.string("abc"),
        ],
      });
    } else {
      tx.moveCall({
        target: `${SUIAI_PACKAGE}::${SUIAI_MODULE}::buy_exact_in_v2`,
        typeArguments: [tokenOut.address],
        arguments: [
          tx.object(SUIAI_CONFIG_OBJECT_ID),
          tx.object(poolObjectId),
          coinTokenIn,
          tx.pure.u64(exactAmountIn.toString()),
          tx.pure.u64(0),
          tx.pure.string("abc"),
        ],
      });
    }
    return { tx, amountOut: NaN };
  };

  private buildSellTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber,
    tokenIn: TCoinMetadata,
    gasBasePrice: bigint,
    coinObjs: (CoinStruct & { owner: string })[],
    poolObjectId: string,
    tokenOut: TCoinMetadata = SUI_TOKEN_METADATA
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    if (coinObjs.length > 1) {
      tx.mergeCoins(
        coinObjs[0].coinObjectId,
        coinObjs.slice(1).map((coin) => coin.coinObjectId)
      );
    }

    let isPairWithSui = false;
    if (
      tokenOut.address === SUI_TOKEN_ADDRESS_SHORT ||
      tokenOut.address === SUI_TOKEN_ADDRESS_FULL
    ) {
      isPairWithSui = true;
    }

    if (isPairWithSui) {
      tx.moveCall({
        target: `${SUIAI_PACKAGE}::${SUIAI_MODULE}::sell_exact_in_v1`, // package
        typeArguments: [tokenIn.address],
        arguments: [
          tx.object(
            SUIAI_CONFIG_OBJECT_ID // dex config cetus
          ),
          tx.object(
            poolObjectId // pool address
          ),
          tx.object(coinObjs[0].coinObjectId),
          tx.pure.u64(exactAmountIn.toString()), // amountIn
          tx.pure.u64(0), // amountOutMin
          tx.pure.string("abc"), // orderId
        ],
      });
    } else {
      tx.moveCall({
        target: `${SUIAI_PACKAGE}::${SUIAI_MODULE}::sell_exact_in_v2`, // package
        typeArguments: [tokenIn.address],
        arguments: [
          tx.object(
            SUIAI_CONFIG_OBJECT_ID // dex config cetus
          ),
          tx.object(
            poolObjectId // pool address
          ),
          tx.object(coinObjs[0].coinObjectId),
          tx.pure.u64(exactAmountIn.toString()), // amountIn
          tx.pure.u64(0), // amountOutMin
          tx.pure.string("abc"), // orderId
        ],
      });
    }
    return { tx, amountOut: NaN };
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata = SUI_TOKEN_METADATA,
    tokenOut: TCoinMetadata,
    poolObjectId: string | undefined,
    gasBasePrice: bigint,
    isBuyBySuiToken = false
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    return this.buildBuyTransaction(
      walletAddress,
      amountIn,
      tokenOut,
      gasBasePrice,
      poolObjectId,
      tokenIn,
      isBuyBySuiToken
    );
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata = SUI_TOKEN_METADATA,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      tokenIn,
      gasBasePrice,
      coinObjs,
      poolObjectId,
      tokenOut
    );
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const result = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.token,
      gasBasePrice,
      tokenCoinObjects,
      position.poolId,
      position.tokenQuote
    );
    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };
}
