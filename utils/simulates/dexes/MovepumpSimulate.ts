import BaseSimulate from "../BaseSimulate";
import { CoinStruct } from "@mysten/sui/client";
import { Transaction } from "@mysten/sui/transactions";
import BigNumber from "bignumber.js";
import { TCoinMetadata, TPosition } from "@/types";
import { isZero } from "@/utils/helper";
import {
  getOwnerCoinOnchain,
  getReferenceGasPrice,
  getSuiClient,
  RETRY_MAX_TIMEOUT,
  RETRY_MAX_ATTEMPT,
  RETRY_MIN_TIMEOUT,
  suiClient,
} from "@/utils/suiClient";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import retry from "async-retry";
import { toStringBN, convertDecToMist } from "@/utils/helper";

interface MoveObjectId {
  id: string;
}

interface MovePumpPoolContent {
  id: MoveObjectId;
  is_completed: boolean;
  real_sui_reserves: any;
  real_token_reserves: any;
  remain_token_reserves: any;
  virtual_sui_reserves: string;
  virtual_token_reserves: string;
}

const MOVEPUMP_PACKAGE =
  "0xb978a1b58e1875c557ff9eeecfab5532ecd38971de895f9222727a9e927cd161";
const MOVEPUMP_MODULE = "move_pump_router";
const MOVEPUMP_FEE_OBJECT_ID =
  "0x04bb2e8a0e4710b8bf8124b1057653036dcac7060094e19a046ec9232f70b319";
const MOVEPUMP_CONFIG_OBJECT_ID =
  "0xd746495d04a6119987c2b9334c5fefd7d8cff52a8a02a3ea4e3995b9a041ace4";
const MOVEPUMP_FEE = 0.005;
const MOVEPUMP_TRADING_FEE = 0.01;
const DEX_INFO =
  "0x3f2d9f724f4a1ce5e71676448dc452be9a6243dac9c5b975a588c8c867066e92";

export default class MovepumpSimulate extends BaseSimulate {
  public name = "MovepumpSimulate";

  public buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
    tokenIn: TCoinMetadata,
    poolObjectId: string,
    gasBasePrice: bigint
  ) => {
    const amountOut = await this.extractBaseTokenOut(
      walletAddress,
      toStringBN(exactAmountIn),
      tokenIn,
      tokenOut,
      poolObjectId
    );
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    const [coin] = tx.splitCoins(tx.gas, [
      tx.pure.u64(exactAmountIn.toString()),
    ]);

    tx.moveCall({
      target: `${MOVEPUMP_PACKAGE}::${MOVEPUMP_MODULE}::buy_exact_out`,
      typeArguments: [
        tokenOut.address, // tokenAddress
      ],
      arguments: [
        tx.object(MOVEPUMP_CONFIG_OBJECT_ID),
        coin,
        tx.object(DEX_INFO),
        tx.pure.u64(convertDecToMist(amountOut, tokenOut.decimals)), //amount_out
        tx.pure.u64(1000000000000), //amount_in_max
        tx.object("0x6"),
        tx.pure.string("abc"), //order id
      ],
    });

    return { tx, amountOut: NaN };
  };

  private getAmountOutExpectOnChain = async (
    poolId: string,
    exactAmountIn: BigNumber
  ) => {
    let client = suiClient;
    return await retry(
      async () => {
        const poolObj = await client.getObject({
          id: poolId,
          options: {
            showContent: true,
          },
        });
        console.log(poolObj);
        const poolContent = poolObj.data!.content;

        if (!poolContent || poolContent.dataType !== "moveObject") {
          throw new Error("Invalid pool content");
        }

        const poolContentFields =
          poolContent.fields as unknown as MovePumpPoolContent;

        const amountInWithFee = exactAmountIn
          .multipliedBy(1.0 - MOVEPUMP_TRADING_FEE!)
          .integerValue(BigNumber.ROUND_FLOOR);

        const amountExpectDecimals = this.calculateUniV2ExactOut(
          amountInWithFee,
          BigNumber(poolContentFields.virtual_sui_reserves),
          BigNumber(poolContentFields.virtual_token_reserves),
          BigNumber(MOVEPUMP_FEE!)
        );

        return amountExpectDecimals;
      },
      {
        retries: RETRY_MAX_ATTEMPT,
        minTimeout: RETRY_MIN_TIMEOUT,
        maxTimeout: RETRY_MAX_TIMEOUT,
        onRetry: (e, attempt) => {
          console.log(`simulateBuyExactOutMovepump retry ${attempt}`, e);
          client = getSuiClient(attempt);
        },
      }
    );
  };

  private calculateUniV2ExactOut = (
    amountIn: BigNumber,
    reserveIn: BigNumber,
    reserveOut: BigNumber,
    fee: BigNumber
  ): BigNumber => {
    const amountInWithFee = amountIn
      .multipliedBy(BigNumber(1).minus(fee))
      .integerValue(BigNumber.ROUND_FLOOR);
    const numerator = amountInWithFee.multipliedBy(reserveOut);
    const denominator = reserveIn.plus(amountInWithFee);
    return numerator.dividedBy(denominator).integerValue(BigNumber.ROUND_FLOOR);
  };

  private buildSellTransaction = (
    walletAddress: string,
    exactAmountIn: BigNumber,
    tokenIn: TCoinMetadata,
    gasBasePrice: bigint,
    coinObjs: (CoinStruct & { owner: string })[]
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    if (coinObjs.length > 1) {
      tx.mergeCoins(
        coinObjs[0].coinObjectId,
        coinObjs.slice(1).map((coin) => coin.coinObjectId)
      );
    }

    tx.moveCall({
      target: `${MOVEPUMP_PACKAGE}::${MOVEPUMP_MODULE}::sell_exact_in`, // package
      typeArguments: [tokenIn.address],
      arguments: [
        tx.object(MOVEPUMP_CONFIG_OBJECT_ID), // configObject,
        tx.object(coinObjs[0].coinObjectId), // tokenInObject
        tx.pure.u64(exactAmountIn.toString()), // amountIn
        tx.pure.u64(0), // amountOutMin
        tx.object("0x6"), // clock
        tx.pure.string("abc"), // orderId
      ],
    });

    return { tx, amountOut: NaN };
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    poolObjectId: string | undefined
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }
    const amountExpectDecimals = await this.getAmountOutExpectOnChain(
      poolObjectId,
      new BigNumber(amountIn)
    );

    const slippage = 0;

    const amountExpect = BigNumber(amountExpectDecimals)
      .multipliedBy(BigNumber(1.0).minus(slippage))
      .dividedBy(BigNumber(10).pow(tokenOut.decimals));

    return amountExpect;
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      tokenIn,
      gasBasePrice,
      coinObjs
    );
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const result = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.token,
      gasBasePrice,
      tokenCoinObjects
    );
    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };
}
