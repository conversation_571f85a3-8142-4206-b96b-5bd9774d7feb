import BaseSimulate from "../BaseSimulate";
import { CoinStruct } from "@mysten/sui/client";
import { Transaction } from "@mysten/sui/transactions";
import BigNumber from "bignumber.js";
import { TCoinMetadata, TPosition } from "@/types";
import { isZero } from "@/utils/helper";
import { getOwnerCoinOnchain, getReferenceGasPrice } from "@/utils/suiClient";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";

const SEVENKFUN_PACKAGE =
  "0x35c12c531895b59534431b1bd27ae41d887991322916f8877528ab3a948b4a2b";
const SEVENKFUN_MODULE = "fun_7k_router";
const SEVENKFUN_FEE_OBJECT_ID =
  "0xf9fc90f1f88b071f53b445af85bed421fd0238b5061dc226e5d60e29ed9bcb28";
const SEVENKFUN_CONFIG_OBJECT_ID =
  "0xe79fff6c77d81f9a8efb729e25bf0037deec42518d40b978f274a4915d7e1ec9";
const SEVENKFUN_AGGREGATOR_CONFIG_OBJECT_ID =
  "0x0f8fc23dbcc9362b72c7a4c5aa53fcefa02ebfbb83a812c8c262ccd2c076d9ee";
const SEVENKFUN_AGGREGATOR_VAULT_OBJECT =
  "0x39a3c55742c0e011b6f65548e73cf589e1ae5e82dbfab449ca57f24c3bcd9514";
const FLOWX_ROUTER =
  "0xb65dcbf63fd3ad5d0ebfbf334780dc9f785eff38a4459e37ab08fa79576ee511";

export default class SevenkfunSimulate extends BaseSimulate {
  public name = "SevenkfunSimulate";

  private buildBuyTransaction = (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
    gasBasePrice: bigint
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    const [coin] = tx.splitCoins(tx.gas, [
      tx.pure.u64(exactAmountIn.toString()),
    ]);

    tx.moveCall({
      target: `${SEVENKFUN_PACKAGE}::${SEVENKFUN_MODULE}::buy_exact_in`, // package
      typeArguments: [
        tokenOut.address, // tokenAddress
      ],
      arguments: [
        tx.object(SEVENKFUN_CONFIG_OBJECT_ID), // configObject,
        coin,
        tx.pure.u64(0), // amountOutMin
        tx.object("0x6"),
        tx.object(FLOWX_ROUTER),
        tx.pure.string("abc"), // orderId
        tx.object(SEVENKFUN_AGGREGATOR_CONFIG_OBJECT_ID!),
        tx.object(SEVENKFUN_AGGREGATOR_VAULT_OBJECT!),
      ],
    });

    return { tx, amountOut: NaN };
  };

  private buildSellTransaction = (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenIn: TCoinMetadata,
    gasBasePrice: bigint,
    coinObjs: (CoinStruct & { owner: string })[]
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    if (coinObjs.length > 1) {
      tx.mergeCoins(
        coinObjs[0].coinObjectId,
        coinObjs.slice(1).map((coin) => coin.coinObjectId)
      );
    }

    tx.moveCall({
      target: `${SEVENKFUN_PACKAGE}::${SEVENKFUN_MODULE}::sell_exact_in`, // package
      typeArguments: [tokenIn.address],
      arguments: [
        tx.object(SEVENKFUN_CONFIG_OBJECT_ID), // configObject,
        tx.object(coinObjs[0].coinObjectId),
        tx.pure.u64(exactAmountIn.toString()), // amountIn
        tx.pure.u64(0), // amountOutMin
        tx.pure.string("abc"), // orderId
        tx.object(SEVENKFUN_AGGREGATOR_CONFIG_OBJECT_ID!),
        tx.object(SEVENKFUN_AGGREGATOR_VAULT_OBJECT!),
      ],
    });

    return { tx, amountOut: NaN };
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    return this.buildBuyTransaction(
      walletAddress,
      amountIn,
      tokenOut,
      gasBasePrice
    );
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      tokenIn,
      gasBasePrice,
      coinObjs
    );
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const result = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.token,
      gasBasePrice,
      tokenCoinObjects
    );
    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };
}
