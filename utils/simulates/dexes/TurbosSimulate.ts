import { Transaction } from "@mysten/sui/transactions";
import BigNumber from "bignumber.js";
import { isZero } from "@/utils/helper";
import {
  getOwnerCoinOnchain,
  getReferenceGasPrice,
  getSuiObject,
} from "@/utils/suiClient";
import { SUI_TOKEN_ADDRESS_SHORT } from "@/utils/contants";
import { TCoinMetadata, TPosition } from "@/types";
import { CoinStruct } from "@mysten/sui/client";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import BaseSimulate from "../BaseSimulate";

const TURBOS_PACKAGE =
  "0xa4cf5943a6203a10da865b91b9d20c349a61e5c8cd4175d5407f6064939b8fc7";
const TURBOS_MODULE = "turbos_router";
const TURBOS_CONFIG_OBJECT_ID =
  "0xf1cf0e81048df168ebeb1b8030fad24b3e0b53ae827c25053fff0779c1445b6f";

export default class TurbosSimulate extends BaseSimulate {
  public name = "TurbosSimulate";

  private buildBuyTransaction = (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
    gasBasePrice: bigint,
    feeTierAddress: string,
    poolObjectId: string
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    const [coin] = tx.splitCoins(tx.gas, [
      tx.pure.u64(exactAmountIn.toString()),
    ]);

    tx.moveCall({
      target: `${TURBOS_PACKAGE}::${TURBOS_MODULE}::buy_exact_in`, // package
      typeArguments: [
        tokenOut.address, // tokenAddress
        SUI_TOKEN_ADDRESS_SHORT,
        feeTierAddress,
      ],
      arguments: [
        tx.object(poolObjectId),
        tx.moveCall({
          target: `0x2::coin::zero`,
          typeArguments: [tokenOut.address],
        }),
        coin,
        tx.pure.u64(0), // amountOutMin
        tx.pure.u128("79226673515401279992447579055"),
        tx.pure.u64(Date.now().toString() + 5 * 60 * 1000),
        tx.object("0x6"),
        tx.object(TURBOS_CONFIG_OBJECT_ID),
        tx.pure.bool(false),
        tx.pure.string("abc"), // orderId
      ],
    });

    return { tx, amountOut: NaN };
  };

  private buildSellTransaction = (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenIn: TCoinMetadata,
    gasBasePrice: bigint,
    feeTierAddress: string,
    poolObjectId: string,
    coinObjs: (CoinStruct & { owner: string })[]
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    if (coinObjs.length > 1) {
      tx.mergeCoins(
        coinObjs[0].coinObjectId,
        coinObjs.slice(1).map((coin) => coin.coinObjectId)
      );
    }

    tx.moveCall({
      target: `${TURBOS_PACKAGE}::${TURBOS_MODULE}::sell_exact_in`, // package
      typeArguments: [
        tokenIn.address, // tokenAddress
        SUI_TOKEN_ADDRESS_SHORT,
        feeTierAddress,
      ],
      arguments: [
        tx.object(poolObjectId),
        tx.object(coinObjs[0].coinObjectId), // tokenInObject
        tx.moveCall({
          target: `0x2::coin::zero`,
          typeArguments: [SUI_TOKEN_ADDRESS_SHORT],
        }),
        tx.pure.u64(exactAmountIn.toString()), // amountIn
        tx.pure.u64(0), // amountOutMin
        tx.pure.u128("4295048016"),
        tx.pure.u64(Date.now().toString() + 5 * 60 * 1000),
        tx.object("0x6"),
        tx.object(TURBOS_CONFIG_OBJECT_ID),
        tx.pure.bool(true),
        tx.pure.string("abc"), // orderId
      ],
    });

    return { tx, amountOut: NaN };
  };

  private getFeeTierAddressFromPooType = (poolType: string) => {
    return poolType.split(",")[2].trim().slice(0, -1);
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const poolObject = await getSuiObject({
      id: poolObjectId,
      options: {
        showType: true,
      },
    });

    const feeTierAddress = this.getFeeTierAddressFromPooType(
      poolObject.data?.type || ""
    );

    return this.buildBuyTransaction(
      walletAddress,
      amountIn,
      tokenOut,
      gasBasePrice,
      feeTierAddress,
      poolObjectId
    );
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );
    const poolObject = await getSuiObject({
      id: poolObjectId,
      options: {
        showType: true,
      },
    });

    const feeTierAddress = this.getFeeTierAddressFromPooType(
      poolObject.data?.type || ""
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      tokenIn,
      gasBasePrice,
      feeTierAddress,
      poolObjectId,
      coinObjs
    );
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const poolObject = await getSuiObject({
      id: position.poolId,
      options: {
        showType: true,
      },
    });

    const feeTierAddress = this.getFeeTierAddressFromPooType(
      poolObject.data?.type || ""
    );

    const result = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.token,
      gasBasePrice,
      feeTierAddress,
      position.poolId,
      tokenCoinObjects
    );
    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };
}
