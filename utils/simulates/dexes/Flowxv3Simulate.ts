import { coinWithBalance, Transaction } from "@mysten/sui/transactions";
import BigNumber from "bignumber.js";
import { TCoinMetadata, TPosition } from "@/types";
import {
  getOwnerCoinOnchain,
  getReferenceGasPrice,
  getSuiObject,
} from "@/utils/suiClient";
import BaseSimulate from "../BaseSimulate";
import { SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import { CoinStruct } from "@mysten/sui/client";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import { isZero } from "@/utils/helper";

export const FLOWXV3_PACKAGE =
  "0x750be1526c82819fd7d1318b7572ee0185c2d1177ec5c0f540864564cb6f536b";
export const FLOWXV3_REGISTRY_ID =
  "0x27565d24a4cd51127ac90e4074a841bbe356cca7bf5759ddc14a975be1632abc";
export const FLOWXV3_VERSIONED_ID =
  "0x67624a1533b5aff5d0dfcf5e598684350efd38134d2d245f475524c03a64e656";
const ORDER_ID = "-1";

export default class Flowxv3Simulate extends BaseSimulate {
  public name = "Flowxv3Simulate";

  private buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenQuote: TCoinMetadata,
    tokenBase: TCoinMetadata,
    fee: string,
    gasBasePrice: bigint
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    // Prepare the base coin (what we're selling)
    const isSuiBase = tokenQuote.address === SUI_TOKEN_ADDRESS_FULL;

    const quoteCoin = coinWithBalance({
      balance: BigInt(exactAmountIn.toString()),
      type: tokenQuote.address,
      useGasCoin: isSuiBase,
    });
    const deadline = Date.now() + 3600 * 1000; // 1 hour from now

    tx.moveCall({
      target: `${FLOWXV3_PACKAGE}::flowxv3_router::buy_exact_in`,
      typeArguments: [tokenBase.address, tokenQuote.address],
      arguments: [
        tx.object(FLOWXV3_REGISTRY_ID),
        tx.pure.u64(fee),
        quoteCoin,
        tx.object(FLOWXV3_VERSIONED_ID),
        tx.object("0x6"),
        tx.pure.u64(0), // Min amount out
        tx.pure.u64(deadline),
        tx.pure.string(ORDER_ID),
      ],
    });

    return { tx, amountOut: NaN };
  };

  private buildSellTransaction = async (
    walletAddress: string,
    exactAmountOut: BigNumber,
    tokenQuote: TCoinMetadata,
    tokenBase: TCoinMetadata,
    fee: string,
    gasBasePrice: bigint
  ) => {
    console.log({
      exactAmountOut: exactAmountOut.toString(),
      tokenQuote,
      tokenBase,
      fee,
      gasBasePrice,
    });
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    // Prepare the base coin (what we're selling)
    const isSuiQuote = tokenQuote.address === SUI_TOKEN_ADDRESS_FULL;

    const baseCoin = coinWithBalance({
      balance: BigInt(exactAmountOut.toString()),
      type: tokenQuote.address,
      useGasCoin: isSuiQuote,
    });
    const deadline = Date.now() + 3600 * 1000; // 1 hour from now

    tx.moveCall({
      target: `${FLOWXV3_PACKAGE}::flowxv3_router::sell_exact_in`,
      typeArguments: [tokenQuote.address, tokenBase.address],
      arguments: [
        tx.object(FLOWXV3_REGISTRY_ID),
        tx.pure.u64(fee),
        baseCoin,
        tx.object(FLOWXV3_VERSIONED_ID),
        tx.object("0x6"),
        tx.pure.u64(0), // Min amount out
        tx.pure.u64(deadline),
        tx.pure.string(ORDER_ID),
      ],
    });

    return { tx, amountOut: NaN };
  };

  private getFeeFromPool = async (poolObjectId: string) => {
    const poolObject = await getSuiObject({
      id: poolObjectId,
      options: {
        showContent: true,
      },
    });

    let fee: string;
    const content = poolObject.data?.content;
    if (content && "fields" in content) {
      // @ts-expect-error: fields may not be present on all content types
      fee = content.fields.swap_fee_rate;
    } else {
      throw new Error("Pool object content does not have fields property");
    }

    return fee;
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenQuote: TCoinMetadata,
    tokenBase: TCoinMetadata,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const fee = await this.getFeeFromPool(poolObjectId);

    return this.buildBuyTransaction(
      walletAddress,
      amountIn,
      tokenQuote,
      tokenBase,
      fee,
      gasBasePrice
    );
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenQuote: TCoinMetadata,
    tokenBase: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const fee = await this.getFeeFromPool(poolObjectId);

    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenQuote.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      tokenQuote,
      tokenBase,
      fee,
      gasBasePrice
    );
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const fee = await this.getFeeFromPool(position.poolId);

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const result = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.tokenQuote,
      position.tokenBase,
      fee,
      gasBasePrice
    );
    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };
}
