import { Transaction } from "@mysten/sui/transactions";
import { normalizeStructTag } from "@mysten/sui/utils";
import { getOwnerCoinsOnchain, getReferenceGasPrice } from "@/utils/suiClient";
import { SUI_TOKEN_ADDRESS_SHORT } from "@/utils/contants";
import config from "@/config";
import BigNumber from "bignumber.js";
import { CoinStruct } from "@mysten/sui/client";

export default class BaseSimulate {
  public name = "BaseSimulate";

  public buildSponsoredTransaction = async (tx: Transaction) => {
    const sponsorAddress = config.sponsorAddress;
    const coins = await getOwnerCoinsOnchain(sponsorAddress);
    const suiCoins = coins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
        normalizeStructTag(SUI_TOKEN_ADDRESS_SHORT)
    );
    const gasBasePrice = await getReferenceGasPrice();
    tx.setGasOwner(sponsorAddress);
    tx.setGasPrice(gasBasePrice);
    tx.setGasPayment(
      suiCoins.map((coin) => ({
        objectId: coin.coinObjectId,
        version: coin.version,
        digest: coin.digest,
      }))
    );

    return tx;
  };

  public extractTokenX2YFromPoolType = (poolType: string) => {
    const match = poolType.match(/<(.+)>/);
    if (!match) return {};

    const tokens = match[1].split(",").map((t) => t.trim());
    if (tokens.length !== 2) return {};

    return {
      tokenXAddress: tokens[0],
      tokenYAddress: tokens[1],
    };
  };

  protected calculateRaidenxFee = (exactAmountIn: BigNumber): string => {
    return BigNumber(exactAmountIn)
      .times(config.raidenxFeeRate)
      .integerValue(BigNumber.ROUND_FLOOR)
      .toFixed();
  };

  protected calculateAmountAfterFee = (
    exactAmountIn: BigNumber,
    raidenxFee: string
  ): BigNumber => {
    return BigNumber(exactAmountIn)
      .minus(raidenxFee)
      .integerValue(BigNumber.ROUND_FLOOR);
  };

  protected addRaidenxFeeToTransaction = (
    tx: Transaction,
    coinObjs: (CoinStruct & { owner: string })[],
    raidenxFee: string
  ): Transaction => {
    if (coinObjs.length > 1) {
      tx.mergeCoins(
        coinObjs[0].coinObjectId,
        coinObjs.slice(1).map((coin) => coin.coinObjectId)
      );
    }

    const [raidenxFeeObject] = tx.splitCoins(coinObjs[0].coinObjectId, [
      tx.pure.u64(raidenxFee),
    ]);
    tx.transferObjects([raidenxFeeObject], config.raidenxFeeReceiverAddress);
    return tx;
  };
}
