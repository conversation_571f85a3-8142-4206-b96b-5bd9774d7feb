import React, { useEffect, useState } from "react";
import { TPair, TPairPrice, TPairTransaction, TTrader } from "@/types";
import rf from "@/services/RequestFactory";
import { minusBN, multipliedBN } from "@/utils/helper";
import BigNumber from "bignumber.js";
import { formatNumber, formatShortAddress } from "@/utils/format";
import { AppNumber, AppProgressSingle } from "@/components";
import { CATEGORY_MARKER } from "@/utils/contants";
import Tooltip from "rc-tooltip";
import {
  DolphinIcon,
  PlanktonIcon,
  ShirmpIcon,
  WhaleIcon,
  FishIcon,
  ArrowRight,
} from "@/assets/icons";

export const DetailMarker = ({
  pair,
  markerAddress,
  category,
  pairPrice,
  type,
  activity,
}: {
  pair: TPair;
  pairPrice: TPairPrice;
  markerAddress: string;
  category: string;
  type?: string;
  activity?: TPairTransaction;
}) => {
  const [data, setData] = useState<TTrader>();

  const getInfoTraders = async () => {
    try {
      const res = await rf
        .getRequest("TradersRequest")
        .getInfoTraders(pair?.network, pair?.pairId, {
          pair: pair?.pairId,
          addresses: [markerAddress],
        });
      setData(res.docs[0]);
    } catch (e) {
      console.error(e);
    }
  };

  useEffect(() => {
    if (!pair) return;
    getInfoTraders().then();
  }, [pair?.pairId, markerAddress]);

  const pnl = minusBN(data?.volumeUsdSold || 0, data?.volumeUsdBought || 0);

  const totalUnrealized = minusBN(
    data?.baseAmountBought || 0,
    data?.baseAmountSold || 0
  );

  const totalUnrealizedByUSD = () => {
    if (new BigNumber(totalUnrealized).comparedTo(0) < 0) {
      return "0";
    }

    return multipliedBN(totalUnrealized, pairPrice?.priceUsd);
  };

  const _renderBalance = () => {
    if (!data?.baseAmountBought || +totalUnrealized < 0)
      return (
        <div className="body-xs-regular-10 text-neutral-alpha-500">Unknown</div>
      );
    return (
      <div className="flex min-w-[93px] flex-col items-center">
        <div className="body-xs-regular-10">
          {!!+totalUnrealized ? formatNumber(totalUnrealized, 2) : "0"}{" "}
          <span className="text-neutral-alpha-500 body-xs-regular-10 px-[2px]">
            of
          </span>
          {formatNumber(data.baseAmountBought || 0, 2)}
        </div>
        <AppProgressSingle
          height={2}
          value={totalUnrealized}
          total={data.baseAmountBought || 0}
        />
      </div>
    );
  };

  const getRule = () => {
    switch (category) {
      case CATEGORY_MARKER.PLANKTON: {
        return "Plankton:  <$10 bought or sold";
      }
      case CATEGORY_MARKER.SHRIMP: {
        return "Shrimp: $250 - $1k bought or sold";
      }
      case CATEGORY_MARKER.FISH: {
        return "Fish: $10 - $250 bought or sold";
      }
      case CATEGORY_MARKER.DOLPHIN: {
        return "Dolphin: $1k - $10k bought or sold";
      }
      case CATEGORY_MARKER.WHALE: {
        return "Whale: >$10k bought or sold";
      }
      default: {
        return "";
      }
    }
  };

  if (type === "compact") {
    return (
      <div className="bg-white-50 w-full px-2">
        <div className="body-sm-medium-12 border-white-50 flex items-center justify-center gap-1 border-b border-dashed py-3 pb-1">
          {activity?.tradingType === "SELL" ? (
            <>
              <AppNumber value={activity?.baseAmount} />{" "}
              {pair?.tokenBase?.symbol}
            </>
          ) : (
            <>
              <AppNumber value={activity?.quoteAmount} /> SUI
            </>
          )}
          <ArrowRight className="text-white-500" />
          {activity?.tradingType === "SELL" ? (
            <>
              <AppNumber value={activity?.quoteAmount} /> SUI
            </>
          ) : (
            <>
              <AppNumber value={activity?.baseAmount} />{" "}
              {pair?.tokenBase?.symbol}
            </>
          )}
        </div>

        <div className="flex items-center gap-1 py-2.5">
          <CategoryMarker category={category} width={32} />
          <div>
            <div className="text-neutral-alpha-1000 body-xs-medium-10">
              {formatShortAddress(markerAddress, 6, 6)}
            </div>
            <div className="flex gap-4">
              <div className="body-xs-regular-10 text-neutral-alpha-500">
                {getRule()}
              </div>
              <div className="body-xs-regular-10 text-neutral-alpha-500">
                Holder Since:: 5h 50m
              </div>
            </div>
          </div>
        </div>

        <div className="border-white-50 flex justify-between border-b border-dashed py-1">
          <div className="body-xs-regular-10 flex gap-1">
            <div className="text-white-800 w-[72px]">
              <span className="text-white-500 pr-1">(+)</span> Invested
            </div>
            <div className="body-xs-medium-10 text-red-500">
              <AppNumber value={data?.volumeUsdBought} isForUSD />
            </div>
          </div>
          <div className="body-xs-regular-10 flex gap-2">
            <div className="text-white-1000">
              <AppNumber value={data?.baseAmountBought} />
            </div>
            <div className="text-white-500">{data?.boughtTxs} txn</div>
          </div>
        </div>

        <div className="border-white-50 flex justify-between border-b border-dashed py-1">
          <div className="body-xs-regular-10 flex gap-1">
            <div className="text-white-800 w-[72px]">
              <span className="text-white-500 pr-1">(-)</span> Sold
            </div>
            <div className="body-xs-medium-10 text-green-500">
              <AppNumber value={data?.volumeUsdSold} isForUSD />
            </div>
          </div>
          <div className="body-xs-regular-10 flex gap-2">
            <div className="text-white-1000">
              <AppNumber value={data?.baseAmountSold} />
            </div>
            <div className="text-white-500">{data?.soldTxs} txn</div>
          </div>
        </div>

        <div className="border-white-50 flex justify-between border-b border-dashed py-1">
          <div className="body-xs-regular-10 flex gap-1">
            <div className="text-white-800 w-[72px]">
              <span className="text-white-500 pr-1">(=)</span> P&L
            </div>
            <div
              className={`${
                +pnl > 0 ? "text-green-500" : "text-red-500"
              } body-xs-medium-10`}
            >
              <AppNumber value={new BigNumber(pnl).abs().toString()} isForUSD />
            </div>
          </div>
        </div>

        <div className="flex justify-between py-1 pb-3">
          <div className="body-xs-regular-10 flex gap-1">
            <div className="text-white-800 w-[72px]">Unrealized</div>
            <div className={`text-white-1000 body-xs-medium-10`}>
              <AppNumber value={totalUnrealizedByUSD()} isForUSD />
            </div>
          </div>

          <div className="text-white-1000">{_renderBalance()}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-neutral-alpha-50 w-full px-2">
      <table className="w-full">
        <tbody>
          <tr className="body-xs-regular-10 border-neutral-alpha-50 border-b border-dashed">
            <td className="w-[72px] py-2">
              <span className="text-neutral-alpha-500 pr-1">(+)</span> Invested
            </td>
            <td className="body-sm-semibold-12 py-2 text-red-500">
              <AppNumber value={data?.volumeUsdBought} isForUSD />
            </td>
            <td className="px-4 py-2">
              <AppNumber value={data?.baseAmountBought} />
            </td>
            <td className="text-neutral-alpha-500 py-2 pl-4">
              {data?.boughtTxs} txn
            </td>
          </tr>

          <tr className="body-xs-regular-10 border-neutral-alpha-50 border-b border-dashed px-4">
            <td className="w-[72px] py-2">
              <span className="text-neutral-alpha-500 pr-1">(-)</span> Sold
            </td>
            <td className="body-sm-semibold-12 py-2 text-green-500">
              <AppNumber value={data?.volumeUsdSold} isForUSD />
            </td>
            <td className="px-4 py-2">
              <AppNumber value={data?.baseAmountSold} />
            </td>
            <td className="text-neutral-alpha-500 py-2 pl-4">
              {data?.soldTxs} txn
            </td>
          </tr>

          <tr className="body-xs-regular-10 border-neutral-alpha-50 border-b border-dashed">
            <td className="w-[72px] py-2">
              <span className="text-neutral-alpha-500 pr-1">(=)</span> P&L
            </td>
            <td
              className={`body-sm-semibold-12 py-2 ${
                +pnl > 0 ? "text-green-500" : "text-red-500"
              }`}
            >
              <AppNumber value={new BigNumber(pnl).abs().toString()} isForUSD />
            </td>
          </tr>

          <tr className="body-xs-regular-10 border-neutral-alpha-50 border-b border-dashed">
            <td className="w-[72px] py-2">
              <div>Unrealized</div>
            </td>
            <td className="body-sm-semibold-12 py-2">
              <AppNumber value={totalUnrealizedByUSD()} isForUSD />
            </td>
            <td colSpan={2} className="pl-4">
              {_renderBalance()}
            </td>
          </tr>
        </tbody>
      </table>

      <div className="flex items-center gap-1 py-2">
        <CategoryMarker category={category} width={32} />
        <div>
          <div className="text-neutral-alpha-1000 body-sm-semibold-12">
            {formatShortAddress(markerAddress, 6, 6)}
          </div>
          <div className="body-xs-regular-10 text-neutral-alpha-500">
            {getRule()}
          </div>
          <div className="body-xs-regular-10 text-neutral-alpha-500">
            Holder Since:: 5h 50m
          </div>
        </div>
      </div>
    </div>
  );
};

const CategoryMarker = ({
  category,
  width,
}: {
  category: string;
  width: number;
}) => {
  if (category === CATEGORY_MARKER.SHRIMP) {
    return <ShirmpIcon className={`w-[${width}px] h-[${width}px]`} />;
  }

  if (category === CATEGORY_MARKER.WHALE) {
    return <WhaleIcon className={`w-[${width}px] h-[${width}px]`} />;
  }

  if (category === CATEGORY_MARKER.DOLPHIN) {
    return <DolphinIcon className={`w-[${width}px] h-[${width}px]`} />;
  }

  if (category === CATEGORY_MARKER.PLANKTON) {
    return <PlanktonIcon className={`w-[${width}px] h-[${width}px]`} />;
  }

  return <FishIcon className={`w-[${width}px] h-[${width}px]`} />;
};

interface TooltipMakerInsightProps {
  pair: TPair;
  pairPrice: TPairPrice;
  makerAddress: string;
  category: string;
  type?: string;
}

export const TooltipMakerInsight: React.FC<TooltipMakerInsightProps> = ({
  pair,
  pairPrice,
  makerAddress,
  category,
  type,
}) => {
  if (type === "compact") {
    return <CategoryMarker category={category} width={16} />;
  }

  return (
    <Tooltip
      overlayClassName="tooltip-marker"
      overlay={
        <DetailMarker
          pair={pair}
          pairPrice={pairPrice}
          markerAddress={makerAddress}
          category={category}
        />
      }
      placement={"left"}
      showArrow={false}
    >
      <div className="cursor-pointer">
        <CategoryMarker category={category} width={16} />
      </div>
    </Tooltip>
  );
};

export default TooltipMakerInsight;
