import { AppLogoNetwork } from "@/components";
import React from "react";
import { TPair, TPosition } from "@/types";
import { isPairWithSui } from "@/utils/pair";
import { NETWORKS } from "@/utils/contants";
import Image from "next/image";

export const AppAvatarToken = ({
  image,
  className,
  size,
}: {
  image?: string;
  className?: string;
  size?: number;
}) => {
  const sizeImage = size || 35;
  if (!image)
    return (
      <Image
        src={`/images/DefaultToken.png`}
        className={`rounded-full ${className} w-[${sizeImage}px] h-[${sizeImage}px] aspect-square`}
        onError={(e) => {
          e.currentTarget.src = `/images/DefaultToken.png`;
        }}
        loading="lazy"
        alt="avatar"
        width={sizeImage}
        height={sizeImage}
        unoptimized
      />
    );

  return (
    <Image
      src={image}
      className={`rounded-full ${className} w-[${sizeImage}px] h-[${sizeImage}px] aspect-square`}
      loading="lazy"
      alt="avatar"
      onError={(e) => {
        e.currentTarget.src = `/images/DefaultToken.png`;
      }}
      width={sizeImage}
      height={sizeImage}
      unoptimized
    />
  );
};

export const AppAvatarTokenQuote = ({
  pair,
  size = 14,
}: {
  pair: TPair | TPosition;
  size?: number;
}) => {
  if (isPairWithSui(pair)) {
    return (
      <AppLogoNetwork
        network={NETWORKS.SUI}
        className={`w-[${size}px] h-[${size}px]`}
        isBase
      />
    );
  }

  return (
    <AppAvatarToken
      size={size}
      image={pair?.tokenQuote?.logoImageUrl || pair?.tokenQuote?.iconUrl || ""}
    />
  );
};
