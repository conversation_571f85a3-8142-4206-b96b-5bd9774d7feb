"use client";
import clsx from "clsx";
import * as React from "react";
import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import {
  ChevronDownIcon,
  CloseDrawQuickSnipe,
  WalletIcon,
} from "@/assets/icons";
import { AppPopover } from "@/components";
import { AmountForm } from "@/components/Snipe/amount-form";
import AvatarSelectPair from "@/components/Snipe/AvatarSelectPair";
import {
  PLATFORMS_CAN_BUY_WITH_SUAI,
  SUI_ADDRESS,
} from "@/components/Snipe/constant";
import DexSelection from "@/components/Snipe/dex-selection";
import { FooterForm } from "@/components/Snipe/footer-form";
import {
  optionsTargetPairFunzone,
  getTokenNameFromTokenAddress,
} from "@/components/Snipe/helper";
import { SettingsForm } from "@/components/Snipe/settings-form";
import { IShowModalSnipe, ISnipeForm } from "@/components/Snipe/type";
import { WalletSelection } from "@/components/Snipe/wallet-selection";
import { useSnipeWallet } from "@/hooks/useSnipeWallet";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { toastError, toastSuccess } from "@/libs/toast";
import { ModalBottomMobile } from "@/modals";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import {
  setIsShowModalAddWallet,
  setIsShowModalEnableTradingPrivy,
} from "@/store/metadata.store";
import { DEXS, getDexLogoUrl, getDexName } from "@/utils/dex";
import { formatNumber, formatShortAddress } from "@/utils/format";
import { useOrder } from "@/hooks";
import { ModalAutoSellSnipeFunzone } from "@/components/Snipe/auto-sell-form";
import Storage from "@/libs/storage";
import BigNumber from "bignumber.js";
import { calculateMaxGasFee, multipliedBN } from "@/utils/helper";
import Image from "next/image";
import { usePrivyTradingEnablement } from "@/hooks/usePrivyTradingEnablement";

type SnipeFormPartProps = {
  onClose?: () => void;
  isQuickSnipe?: boolean;
};

const SnipeFormPart = ({ onClose, isQuickSnipe }: SnipeFormPartProps) => {
  const dispatch = useDispatch();
  const { accessToken, network, wallets, platformSnipe } = useSelector(
    (state: RootState) => ({
      accessToken: state.user.accessToken,
      network: state.user.network,
      wallets: state.user.wallets,
      platformSnipe: state.metadata.platformSnipe,
    })
  );
  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const [showModal, setShowModal] = useState<IShowModalSnipe>({
    settings: false,
    selectWallet: false,
    modalWalletSelection: false,
    modalSettingsOrder: false,
    options: false,
    targetPair: false,
  });
  const snipeSettings = Storage.getSnipeSettings();
  const [snipeForm, setSnipeForm] = useState<ISnipeForm>({
    buyAmount: "",
    targetWalletAddress: "",
    targetTokenAddress: "",
    // Target Pair
    targetTokenQuoteAddress: SUI_ADDRESS,
    // Choose Token
    buyByToken: SUI_ADDRESS,
  });

  // auto sell
  const [isSettingAutoSell, setIsSettingAutoSell] = useState<boolean>(
    snipeSettings?.snipeFunzone?.autoSell?.isSettingAutoSell || false
  );
  const [isShowSettingAutoSell, setIsShowSettingAutoSell] =
    useState<boolean>(false);
  const [isErrorInsufficientBalance, setIsErrorInsufficientBalance] =
    useState<boolean>(false);
  const [triggersOrder, setTriggersOrder] = useState<any[]>(
    snipeSettings?.snipeFunzone?.autoSell?.triggers || [
      {
        curvePercent: 90,
        sellPercent: 100,
      },
    ]
  );

  const allDexes = platformSnipe.map((item) => item.name);
  const [dexSelected, setDexSelected] = useState<string>(allDexes[0]);
  const { settings } = useOrder();
  const { isPrivyUser, isTradingEnabled } = usePrivyTradingEnablement();

  useEffect(() => {
    setDexSelected(allDexes[0]);
  }, [platformSnipe]);
  const objDexSelected = platformSnipe?.find(
    (item) => item?.name === dexSelected
  );

  const { activeSnipeWallets, activeTotalQuoteBalance } = useSnipeWallet(
    snipeForm.buyByToken
  );

  const gasBudget = useMemo(() => {
    return BigNumber.max(
      new BigNumber(calculateMaxGasFee(settings.snipeGasPrice)).toFixed(
        5,
        BigNumber.ROUND_UP
      ),
      0.05
    ).toString();
  }, [settings.snipeGasPrice]);

  const minBalance = useMemo(() => {
    return BigNumber(
      multipliedBN(+snipeForm.buyAmount, 1 + +settings.snipeSlippage / 100)
    )
      .plus(settings.snipeTipAmount)
      .plus(gasBudget)
      .toString();
  }, [
    snipeForm.buyAmount,
    settings.snipeSlippage,
    settings.snipeTipAmount,
    gasBudget,
  ]);

  const addressWalletsInsufficientBalance = useMemo(() => {
    if (objDexSelected?.dex !== "movepump") {
      return [];
    }

    const walletsInsufficientBalance = activeSnipeWallets.filter((item) =>
      new BigNumber(item.balance).lt(minBalance)
    );

    return walletsInsufficientBalance.map((wallet) =>
      formatShortAddress(wallet.address)
    );
  }, [
    objDexSelected?.dex,
    activeSnipeWallets,
    +snipeForm.buyAmount,
    +settings.snipeSlippage,
    minBalance,
  ]);

  const createOrder = async () => {
    if (!snipeForm.targetWalletAddress) {
      toastError("Error", "Please enter target wallet address");
      return;
    }
    if (!snipeForm.buyAmount) {
      toastError("Error", "Please enter amount");
      return;
    }

    if (isPrivyUser && !isTradingEnabled) {
      dispatch(setIsShowModalEnableTradingPrivy({ isShow: true }));
      return;
    }

    if (
      objDexSelected?.dex === "movepump" &&
      activeSnipeWallets.some((item) =>
        new BigNumber(item.balance).lt(minBalance)
      )
    ) {
      toastError("Error", "Insufficient wallet balance");
      setIsErrorInsufficientBalance(true);
      return;
    }

    try {
      let autoSellSettings = null as any;

      if (isSettingAutoSell) {
        if (!triggersOrder?.length) {
          toastError("Error", "Please setting for auto sell");
          return;
        }

        autoSellSettings = {
          isActive: true,
          receiveToken: null,
          triggers: triggersOrder,
        };
      }

      const res = await rf
        .getRequest("SnipeFunzoneRequest")
        .createSnipeDex(network, {
          autoSellSettings,
          buyAmount: +snipeForm.buyAmount,
          slippage: +settings.snipeSlippage || 0,
          gasPrice: +settings.snipeGasPrice || 0,
          tipAmount: settings.snipeTipAmount,
          buyByToken: snipeForm.buyByToken,
          targetDex: objDexSelected?.dex,
          targetWalletAddress: snipeForm.targetWalletAddress,
          targetTokenQuoteAddress: snipeForm.targetTokenQuoteAddress,
          userWalletAddresses: activeSnipeWallets.map(
            (wallet) => wallet.address
          ),
        });
      const allFailed = res.every((snipe: any) => !snipe.isSuccess);

      if (allFailed) {
        toastError(
          "Error",
          "This snipe listing setting existed, only update or delete"
        );
        return;
      }
      AppBroadcast.dispatch(BROADCAST_EVENTS.REFRESH_DATA_SNIPING_LIST);
      const walletsFailed = res.filter((snipe: any) => !snipe.isSuccess);
      if (walletsFailed.length > 0) {
        toastError(
          "Error",
          `Wallet ${walletsFailed
            .map((wallet: any) => wallet.userWalletAddress)
            .join(", ")} dex listing setting existed, only update or delete`
        );
        return;
      }
      setSnipeForm({
        ...snipeForm,
        targetWalletAddress: "",
        buyAmount: "",
      });
      setDexSelected(allDexes[0]);
      toastSuccess("Success", "Snipe order created successfully!");
    } catch (error: any) {
      toastError("Error", error.message || "Something went wrong!");
    }
  };
  const onShowSettings = () => {
    if (!accessToken) return;
    if (isTabletOrMobile) {
      setShowModal({
        ...showModal,
        modalSettingsOrder: true,
      });
      return;
    }
    setShowModal({
      ...showModal,
      settings: true,
    });
  };

  const onSelectDex = (dex: string) => {
    setShowModal({
      ...showModal,
      options: false,
    });
    setDexSelected(dex);
    if (!PLATFORMS_CAN_BUY_WITH_SUAI.includes(dex)) {
      setSnipeForm((prevForm) => ({
        ...prevForm,
        buyByToken: SUI_ADDRESS,
        targetTokenQuoteAddress: SUI_ADDRESS,
      }));
    }
  };

  const onSelectTargetPair = (targetTokenQuoteAddress: string) => {
    setShowModal({
      ...showModal,
      targetPair: false,
    });
    setSnipeForm({
      ...snipeForm,
      targetTokenQuoteAddress: targetTokenQuoteAddress,
      buyByToken: targetTokenQuoteAddress,
    });
  };

  const toggleSettingAutoSell = () => {
    if (!accessToken) return;

    if (isSettingAutoSell) {
      setIsSettingAutoSell(false);
      Storage.setSnipeSettings({
        ...snipeSettings,
        snipeFunzone: {
          ...snipeSettings?.snipeFunzone,
          autoSell: {
            ...snipeSettings?.snipeFunzone?.autoSell,
            isSettingAutoSell: false,
          },
        },
      });
      return;
    }
    setIsSettingAutoSell(true);
    setIsShowSettingAutoSell(true);

    Storage.setSnipeSettings({
      ...snipeSettings,
      snipeFunzone: {
        ...snipeSettings?.snipeFunzone,
        autoSell: {
          ...snipeSettings?.snipeFunzone?.autoSell,
          isSettingAutoSell: true,
        },
      },
    });
  };

  const _renderContent = () => {
    if (showModal.settings) {
      return (
        <SettingsForm
          onCloseSettings={() =>
            setShowModal({ ...showModal, settings: false })
          }
        />
      );
    }
    if (showModal.selectWallet) {
      return (
        <WalletSelection
          snipeForm={snipeForm}
          onCloseWalletSettings={() =>
            setShowModal({ ...showModal, selectWallet: false })
          }
        />
      );
    }

    return (
      <>
        {isQuickSnipe && (
          <div className="text-white-1000 border-white-100 flex w-full justify-between border-b p-3 text-[14px] font-[500]">
            Quick Snipe
            <CloseDrawQuickSnipe onClick={onClose} className="cursor-pointer" />
          </div>
        )}

        <div className="border-white-100 border-b pb-3">
          <div className="body-sm-regular-12 text-white-500 mb-[8px] flex items-center justify-between">
            <div>Choose Platform</div>
          </div>

          <AppPopover
            customClassWrapper="w-full"
            isOpen={showModal.options}
            onToggle={() => {
              setShowModal({
                ...showModal,
                options: !showModal.options,
              });
            }}
            onClose={() => setShowModal({ ...showModal, options: false })}
            trigger={
              <div
                className={`bg-brand-900 border-brand-800 body-sm-regular-12 flex h-[36px] w-full cursor-pointer items-center
                  justify-between gap-2 rounded-[6px] border px-[8px] capitalize`}
              >
                <div className="flex items-center gap-2">
                  {objDexSelected?.dex ? (
                    <Image
                      src={getDexLogoUrl(
                        objDexSelected.dex as keyof typeof DEXS
                      )}
                      alt={getDexName(objDexSelected.dex as keyof typeof DEXS)}
                      className="h-[16px] w-[16px] rounded-full"
                      width={16}
                      height={16}
                      unoptimized
                    />
                  ) : null}
                  {dexSelected}
                </div>
                <ChevronDownIcon
                  className={`duration-400 h-4 w-4 ${
                    showModal.options ? "rotate-[180deg]" : ""
                  }`}
                />
              </div>
            }
            content={
              <DexSelection
                dexSelected={dexSelected}
                onSelectDex={onSelectDex}
                listDex={platformSnipe}
              />
            }
            position="left"
          />
        </div>

        <div className="border-white-50 bg-black-900 my-3 flex h-full max-h-[34px] items-center gap-2 rounded border px-1 pl-1 pr-2">
          <div className="text-white-800 border-white-50 border-r pl-[2px] pr-1 text-[12px] font-normal leading-[18px]">
            Dev Wallet
          </div>
          <input
            value={snipeForm.targetWalletAddress}
            onChange={(e) =>
              setSnipeForm({
                ...snipeForm,
                targetWalletAddress: e.target.value,
              })
            }
            type="text"
            className="text-white-1000 placeholder:text-white-200 bg-black-900 flex-1 text-[12px] font-normal leading-[18px] focus:outline-none"
            placeholder="Add target wallet address"
          />
        </div>

        {optionsTargetPairFunzone(dexSelected).length > 1 && (
          <div className="my-3">
            <div className="body-sm-regular-12 text-white-500 mb-[8px] flex items-center justify-between">
              <div>Select Target Pair</div>
            </div>

            <AppPopover
              customClassWrapper="w-full"
              isOpen={showModal.targetPair}
              onToggle={() => {
                setShowModal({
                  ...showModal,
                  targetPair: !showModal.targetPair,
                });
              }}
              onClose={() => setShowModal({ ...showModal, targetPair: false })}
              trigger={
                <div
                  className={`bg-neutral-alpha-50 body-sm-regular-12 flex h-[36px] w-full cursor-pointer items-center justify-between
                  gap-2 rounded-[6px] border-none px-[8px] capitalize`}
                >
                  <div className="flex items-center gap-2">
                    {`Token/${getTokenNameFromTokenAddress(
                      snipeForm.targetTokenQuoteAddress
                    )}`}
                  </div>
                  <ChevronDownIcon
                    className={`duration-400 h-4 w-4 ${
                      showModal.targetPair ? "rotate-[180deg]" : ""
                    }`}
                  />
                </div>
              }
              content={
                <div className="flex w-full flex-col flex-wrap gap-2 rounded-[8px] bg-[#212224] p-[4px]">
                  {optionsTargetPairFunzone(dexSelected).map((item, index) => {
                    return (
                      <div
                        onClick={() => onSelectTargetPair(item?.value)}
                        key={index}
                        className={`${
                          snipeForm.targetTokenQuoteAddress === item?.value
                            ? "border-white-100 bg-white-100"
                            : "border-white-50"
                        }
                      body-sm-regular-12 hover:border-white-100 flex h-[36px] cursor-pointer items-center gap-2 rounded-[6px] border px-[8px]`}
                      >
                        <span>{item?.label}</span>
                      </div>
                    );
                  })}
                </div>
              }
              position="left"
            />
          </div>
        )}

        <div className="">
          {!!wallets.length ? (
            <div
              onClick={() => setShowModal({ ...showModal, selectWallet: true })}
              className="bg-neutral-alpha-50 flex cursor-pointer items-center justify-between rounded-[4px] p-[4px]"
            >
              <div className="flex items-center gap-2">
                <div className="tablet:bg-neutral-beta-500 flex h-6 w-6 items-center justify-center rounded-[4px]">
                  <WalletIcon />
                </div>
                <div className="flex gap-2">
                  <div className="body-sm-semibold-12">Wallet</div>
                  <div className="body-xs-regular-10 text-brand-500 bg-brand-800 border-brand-800 rounded-[80px] border px-[6px]">
                    {activeSnipeWallets.length} selected
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  <div className="body-sm-regular-12">
                    {formatNumber(activeTotalQuoteBalance, 4)}
                  </div>
                  <AvatarSelectPair
                    coin={getTokenNameFromTokenAddress(snipeForm.buyByToken)}
                  />
                </div>

                <div>
                  <ChevronDownIcon className="text-neutral-alpha-500 h-[16px] w-[16px] rotate-[-90deg]" />
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-neutral-alpha-50 flex items-center justify-between rounded-[4px] p-[4px]">
              <div className="flex items-center gap-2">
                <div className="bg-neutral-beta-500 flex h-6 w-6 items-center justify-center rounded-[4px]">
                  <WalletIcon />
                </div>
                <div className="flex gap-2">
                  <div className="body-sm-semibold-12">Wallet</div>
                </div>
              </div>
              {accessToken && (
                <div
                  className="action-xs-medium-12 text-brand-500 cursor-pointer"
                  onClick={() => {
                    dispatch(setIsShowModalAddWallet({ isShow: true }));
                  }}
                >
                  Add Wallet
                </div>
              )}
            </div>
          )}
        </div>

        <div>
          <AmountForm setSnipeForm={setSnipeForm} snipeForm={snipeForm} />
        </div>

        <FooterForm
          snipeForm={snipeForm}
          createOrder={createOrder}
          balance={activeTotalQuoteBalance}
          onShowSettings={onShowSettings}
          toggleSetAutoSell={toggleSettingAutoSell}
          autoSell={isSettingAutoSell}
          onShowSettingAutoSell={() => setIsShowSettingAutoSell(true)}
        />

        {isErrorInsufficientBalance &&
          !!addressWalletsInsufficientBalance.length && (
            <div className="body-sm-regular-12 mt-2 whitespace-normal text-red-500">
              On movepump, you need to have a balance = buy_amount * (1 +
              slippage) + tip_amount + gas_budget for the snipe to success. In
              your case {+snipeForm.buyAmount} * (1 + {+settings.snipeSlippage}
              %) + {settings.snipeTipAmount} + {gasBudget} = {minBalance} SUI at
              least. <br /> Wallets insufficient balance:{" "}
              {addressWalletsInsufficientBalance.join(", ")}
            </div>
          )}

        <ModalAutoSellSnipeFunzone
          isOpen={isShowSettingAutoSell}
          triggersOrder={triggersOrder}
          setTriggersOrder={setTriggersOrder}
          onClose={() => setIsShowSettingAutoSell(false)}
        />
      </>
    );
  };

  return (
    <div
      className={clsx(
        "bg-neutral-alpha-50 tablet:pr-[8px] border-neutral-alpha-50 tablet:overflow-hidden flex h-full w-full overflow-auto border-b px-[8px] py-[12px]",
        showModal.selectWallet ? "pr-2" : "pr-2"
      )}
    >
      <div className="w-full">{_renderContent()}</div>

      {showModal.modalWalletSelection && (
        <ModalBottomMobile
          isOpen={showModal.modalWalletSelection}
          onClose={() =>
            setShowModal({ ...showModal, modalWalletSelection: false })
          }
        >
          <div className="w-full p-[16px]">
            <WalletSelection
              snipeForm={snipeForm}
              onCloseWalletSettings={() =>
                setShowModal({ ...showModal, selectWallet: false })
              }
            />
          </div>
        </ModalBottomMobile>
      )}

      {showModal.modalSettingsOrder && (
        <ModalBottomMobile
          isOpen={showModal.modalSettingsOrder}
          onClose={() =>
            setShowModal({ ...showModal, modalSettingsOrder: false })
          }
        >
          <div className="w-full p-[16px]">
            <SettingsForm
              onCloseSettings={() =>
                setShowModal({ ...showModal, settings: false })
              }
            />
          </div>
        </ModalBottomMobile>
      )}
    </div>
  );
};
export default SnipeFormPart;
