import BigNumber from "bignumber.js";
import * as React from "react";
import { useContext, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { WalletIcon } from "@/assets/icons";
import { AppDropdown, AppNumber, AppSymbolToken } from "@/components";
import { useOrder, useUser, useAggregatorAutoToggle } from "@/hooks";

import { RootPairContext } from "@/app/sui/(token_detail)/provider";
import { RootState, AppDispatch } from "@/store";
import {
  setIsShowModalAddWallet,
  setIsShowModalEnableTradingPrivy,
} from "@/store/metadata.store";
import { TPair, TPairPrice } from "@/types";
import { AmountForm } from "./buy-order/AmountForm";
import { FooterForm } from "./buy-order/FooterForm";
import { EDex, OrderFormType, OrderLimitTargetType } from "@/enums";
import {
  convertMistToDec,
  dividedBN,
  minusBN,
  multipliedBN,
  sleep,
  toStringBN,
} from "@/utils/helper";
import Storage from "@/libs/storage";
import { useEffect } from "react";
import { isEmpty } from "lodash";
import {
  getCirculatingSupply,
  isBuyBySuiToken,
  isPairWithSui,
} from "@/utils/pair";
import { useMediaQuery } from "react-responsive";
import { TargetForm } from "./buy-order/TargetForm";
import { SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import { DCAForm } from "./buy-order/DCAForm";
import { OPTIONS_ORDER_TYPE } from "@/utils/contants";
import { ModalAutoSell } from "./buy-order/AutoSellForm";
import { toastError } from "@/libs/toast";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { AggregatorToggle } from "./AggregatorToggle";
import { usePrivyTradingEnablement } from "@/hooks/usePrivyTradingEnablement";

export const OrderFormBuy = ({
  onShowSettings,
  onShowSelectWallet,
  totalBalanceTokenBase,
  tokenQuoteSelected,
  setTokenQuoteSelected,
  setOrderType,
  orderType,
}: {
  onShowSettings: () => void;
  onShowSelectWallet: () => void;
  totalBalanceTokenBase: string | number;
  tokenQuoteSelected: string;
  setTokenQuoteSelected: (value: string) => void;
  setOrderType: (value: string) => void;
  orderType: any;
}) => {
  const userSettings = Storage.getUserSettings();
  const orderSettings = Storage.getOrderSettings();

  const [amount, setAmount] = useState<any>("");
  const [percent, setPercent] = useState<any>("0");
  // const [orderType, setOrderType] = useState<any>(OrderFormType.MARKET);

  // for Limit
  const [targetMC, setTargetMC] = useState<any>("");
  const [targetPrice, setTargetPrice] = useState<any>("");
  const [targetType, setTargetType] = useState<any>(
    userSettings.orderLimitTargetType || OrderLimitTargetType.MC
  );

  // for DCA
  const [marketCapTo, setMarketCapTo] = useState<any>("");
  const [marketCapFrom, setMarketCapFrom] = useState<any>("");
  const [resolution, setResolution] = useState<string>("H");
  const [amountTime, setAmountTime] = useState<any>("");
  const [amountOrders, setAmountOrders] = useState<any>("");
  const [isSelectMCRange, setIsSelectMCRange] = useState<boolean>(false);

  //autoSell
  const [isSettingAutoSell, setIsSettingAutoSell] = useState<boolean>(
    orderSettings?.autoSell?.isSettingAutoSell || false
  );
  const [isShowSettingAutoSell, setIsShowSettingAutoSell] =
    useState<boolean>(false);
  const [receiveToken, setReceiveToken] = useState<string | null>(
    orderSettings?.autoSell?.receiveToken || null
  );
  const [triggersOrder, setTriggersOrder] = useState<any[]>(
    orderSettings?.autoSell?.triggers || [
      {
        priceChangePercent: 10,
        sellPercent: 50,
      },
      {
        priceChangePercent: -10,
        sellPercent: 50,
      },
    ]
  );

  const { pair, pairPrice, poolObjects } = useContext(RootPairContext) as {
    pair: TPair;
    pairPrice: TPairPrice;
    poolObjects: any;
  };

  const { useAggregator, setUseAggregator } = useAggregatorAutoToggle(
    pair?.createdAt
  );

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const { quickBuy, buyLimit, buyDCA } = useOrder();
  const { activeWallets, activeTotalSuiBalance } = useUser();
  const { isPrivyUser, isTradingEnabled } = usePrivyTradingEnablement();

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  // get amount can buy with Moonbags Dex
  const amountCanBuyWithMoonBagsDex = useMemo(() => {
    if (
      !!poolObjects &&
      pair?.dex?.dex === EDex.MOONBAGS &&
      Number(pair?.bondingCurve || 0) < 1
    ) {
      return convertMistToDec(
        minusBN(
          poolObjects?.threshold,
          poolObjects?.real_sui_reserves?.fields?.balance
        )
      );
    }

    return amount;
  }, [poolObjects, amount, pair?.bondingCurve, pair?.dex?.dex]);

  const dispatch = useDispatch<AppDispatch>();

  const onAddWallet = () => {
    dispatch(setIsShowModalAddWallet({ isShow: true }));
  };

  const getBuyByToken = () => {
    if (isPairWithSui(pair)) {
      return "";
    }

    if (isBuyBySuiToken(tokenQuoteSelected)) {
      return SUI_TOKEN_ADDRESS_FULL;
    }

    return "";
  };

  const creatOrderBuyMarket = async () => {
    let autoSellSettings = null as any;
    if (isSettingAutoSell) {
      if (!triggersOrder?.length) {
        toastError("Error", "Please setting for auto sell");
        return;
      }

      autoSellSettings = {
        receiveToken: isPairWithSui(pair) ? null : receiveToken,
        triggers: triggersOrder,
      };
    }

    const maxAmountCanBuy = BigNumber.min(
      amount,
      amountCanBuyWithMoonBagsDex
    ).toString();

    await quickBuy(
      autoSellSettings,
      pair,
      maxAmountCanBuy,
      getBuyByToken(),
      useAggregator,
      async () => {
        setAmount("");
        await sleep(2000);
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
      }
    ).then();
  };

  const creatOrderBuyLimit = async () => {
    let priceTarget = targetPrice;
    if (targetType === OrderLimitTargetType.MC) {
      priceTarget = dividedBN(targetMC, getCirculatingSupply(pair));
    }

    let autoSellSettings = null as any;
    if (isSettingAutoSell) {
      if (!triggersOrder?.length) {
        toastError("Error", "Please setting for auto sell");
        return;
      }

      autoSellSettings = {
        receiveToken: isPairWithSui(pair) ? null : receiveToken,
        triggers: triggersOrder,
      };
    }

    await buyLimit(
      autoSellSettings,
      pair,
      amount,
      priceTarget,
      getBuyByToken(),
      useAggregator,
      () => {
        setAmount("");
      }
    ).then();
  };

  const creatOrderBuyDCA = async () => {
    let tokenPriceRange = {
      min: null,
      max: null,
    };

    if (isSelectMCRange) {
      let priceMin = null;
      let priceMax = null;

      if (!!marketCapFrom) {
        priceMin = dividedBN(marketCapFrom, getCirculatingSupply(pair));
      }

      if (!!marketCapTo) {
        priceMax = dividedBN(marketCapTo, getCirculatingSupply(pair));
      }

      tokenPriceRange = {
        min: priceMin,
        max: priceMax,
      } as any;
    }

    const getInterval = () => {
      if (!amountTime) return "";
      if (resolution === "D") return amountTime * 86400;
      if (resolution === "H") return amountTime * 3600;
      if (resolution === "M") return amountTime * 60;
      return "";
    };

    await buyDCA(
      pair,
      amount,
      getInterval(),
      tokenPriceRange,
      amountOrders,
      getBuyByToken(),
      useAggregator,
      () => {
        setAmount("");
        setAmountOrders("");
        setAmountTime("");
        setIsSelectMCRange(false);
      }
    ).then();
  };

  const createOrder = async () => {
    if (new BigNumber(amount).isZero()) return;

    if (isPrivyUser && !isTradingEnabled) {
      dispatch(setIsShowModalEnableTradingPrivy({ isShow: true }));
      return;
    }

    if (orderType === OrderFormType.MARKET) {
      creatOrderBuyMarket().then();
      return;
    }

    if (orderType === OrderFormType.DCA) {
      creatOrderBuyDCA().then();
      return;
    }

    creatOrderBuyLimit().then();
  };

  useEffect(() => {
    if (isEmpty(pair)) return;
    setTargetMC(multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair)));
    setTargetPrice(toStringBN(pairPrice?.priceUsd));
    Storage.setUserSettings("orderLimitTargetType", targetType);
  }, [pair?.pairId, targetType]);

  useEffect(() => {
    if (isEmpty(pair) || orderType !== OrderFormType.DCA) return;
    const marketCap = multipliedBN(
      pairPrice?.priceUsd,
      getCirculatingSupply(pair)
    );

    setMarketCapTo(multipliedBN(marketCap, 1.1));
    setMarketCapFrom(multipliedBN(marketCap, 0.9));
  }, [pair?.pairId, orderType]);

  useEffect(() => {
    setAmount("");
  }, [tokenQuoteSelected]);

  useEffect(() => {
    if (!accessToken) {
      setIsSettingAutoSell(false);
    }
  }, [accessToken]);

  const _renderHeaderForm = () => {
    if (isMobile) {
      const _renderWallet = () => {
        if (!!wallets.length) {
          return (
            <div
              onClick={onShowSelectWallet}
              className="bg-neutral-alpha-50 flex w-fit cursor-pointer items-center justify-between rounded-[4px] p-[4px]"
            >
              <div className="flex items-center gap-2">
                <div className=" flex h-6 w-6 items-center justify-center rounded-[4px]">
                  <WalletIcon />
                </div>

                <div className="body-xs-regular-10 text-brand-500 bg-brand-800 border-brand-800 rounded-[80px] border px-[6px]">
                  {activeWallets.length}
                </div>
              </div>
            </div>
          );
        }

        return (
          <div className="flex items-center gap-2">
            <div className="bg-neutral-alpha-50 flex items-center justify-between rounded-[4px] px-4 py-1">
              <div className="flex h-6 w-6 items-center justify-center rounded-[4px]">
                <WalletIcon />
              </div>
            </div>
            <div className="text-white-500 whitespace-nowrap text-[12px]">
              0 wallet
            </div>
          </div>
        );
      };

      return (
        <div className="flex gap-4">
          <div>{_renderWallet()}</div>

          <div className="flex w-full items-center justify-end gap-2">
            {!!wallets.length ? (
              <div className="flex items-center gap-2">
                <div className="body-sm-regular-12 text-white-500 border-white-50 flex border-r pr-2">
                  <AppNumber
                    value={activeTotalSuiBalance}
                    className="text-white-800 pr-1"
                  />
                  SUI
                </div>
                <div className="body-sm-regular-12 text-white-500 flex">
                  <AppNumber
                    value={totalBalanceTokenBase}
                    className="text-white-800 pr-1"
                  />
                  <AppSymbolToken
                    symbol={pair?.tokenBase?.symbol || "Unknown"}
                  />
                </div>
              </div>
            ) : (
              accessToken && (
                <div
                  className="text-brand-500 cursor-pointer text-[12px] font-medium"
                  onClick={onAddWallet}
                >
                  Add wallet
                </div>
              )
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="flex gap-4">
        <div className="flex w-full items-center justify-end gap-2">
          <div className="border-neutral-alpha-50 flex flex-1 gap-3 border-b pb-1">
            {OPTIONS_ORDER_TYPE.map((item, index) => {
              return (
                <div
                  key={index}
                  onClick={() => {
                    setOrderType(item.value);
                  }}
                  className={`mb-[-4px] cursor-pointer 
                   ${
                     orderType === item.value
                       ? "text-white-1000 border-neutral-alpha-500 body-sm-semibold-12 border-b"
                       : "text-white-500 action-xs-medium-12"
                   }`}
                >
                  {item.name}
                </div>
              );
            })}
          </div>

          {!!wallets.length ? (
            <div className="flex items-center gap-2">
              <div className="body-sm-regular-12 text-white-500 flex">
                <AppNumber
                  value={totalBalanceTokenBase}
                  className="text-white-800 pr-1"
                />
                <AppSymbolToken symbol={pair?.tokenBase?.symbol || "Unknown"} />
              </div>
            </div>
          ) : (
            accessToken && (
              <div
                className="text-brand-500 cursor-pointer text-[12px] font-medium"
                onClick={onAddWallet}
              >
                Add wallet
              </div>
            )
          )}
        </div>
      </div>
    );
  };

  const _renderTargetForm = () => {
    if (orderType === OrderFormType.MARKET || orderType === OrderFormType.DCA) {
      return (
        <div className="body-sm-regular-12 text-white-300 w-full text-center">
          Market Price
        </div>
      );
    }
    return (
      <div className="flex-1">
        <TargetForm
          targetType={targetType}
          setTargetType={setTargetType}
          targetMC={targetMC}
          setTargetMC={setTargetMC}
          targetPrice={targetPrice}
          setTargetPrice={setTargetPrice}
          pair={pair}
        />
      </div>
    );
  };

  const toggleSettingAutoSell = () => {
    if (!accessToken) return;

    if (isSettingAutoSell) {
      setIsSettingAutoSell(false);
      Storage.setOrderSettings({
        ...orderSettings,
        autoSell: {
          ...orderSettings.autoSell,
          isSettingAutoSell: false,
        },
      });
      return;
    }
    setIsSettingAutoSell(true);
    setIsShowSettingAutoSell(true);

    Storage.setOrderSettings({
      ...orderSettings,
      autoSell: {
        ...orderSettings.autoSell,
        isSettingAutoSell: true,
      },
    });
  };

  const onOpenSettingAutoSell = () => {
    if (!accessToken) return;
    setIsShowSettingAutoSell(true);
  };

  return (
    <div>
      {_renderHeaderForm()}

      {isMobile && (
        <div className="tablet bg-black-900 border-white-50 mt-3 flex h-[34px] items-center rounded-[4px] border">
          <AppDropdown
            options={OPTIONS_ORDER_TYPE}
            onSelect={(value) => {
              setOrderType(value);
            }}
            className="border-white-50 w-[64px] rounded-none border-r border-solid"
            value={orderType}
          />

          {_renderTargetForm()}
        </div>
      )}

      <AmountForm
        amount={amount}
        setAmount={setAmount}
        pair={pair}
        percent={percent}
        setPercent={setPercent}
        setTokenQuoteSelected={setTokenQuoteSelected}
        tokenQuoteSelected={tokenQuoteSelected}
        hideSliderRange={orderType === OrderFormType.DCA}
      />

      {orderType === OrderFormType.LIMIT && !isMobile && (
        <div className="border-neutral-alpha-50 mt-6 overflow-hidden rounded-[8px] border">
          <TargetForm
            targetType={targetType}
            setTargetType={setTargetType}
            targetMC={targetMC}
            setTargetMC={setTargetMC}
            targetPrice={targetPrice}
            setTargetPrice={setTargetPrice}
            pair={pair}
          />
        </div>
      )}

      {orderType === OrderFormType.DCA && (
        <DCAForm
          pair={pair}
          marketCapTo={marketCapTo}
          marketCapFrom={marketCapFrom}
          resolution={resolution}
          amountOrders={amountOrders}
          amountTime={amountTime}
          isSelectMCRange={isSelectMCRange}
          setMarketCapTo={setMarketCapTo}
          setMarketCapFrom={setMarketCapFrom}
          setResolution={setResolution}
          setAmountOrders={setAmountOrders}
          setAmountTime={setAmountTime}
          setIsSelectMCRange={setIsSelectMCRange}
        />
      )}

      <FooterForm
        pair={pair}
        autoSell={isSettingAutoSell}
        toggleSetAutoSell={toggleSettingAutoSell}
        onShowSettingAutoSell={onOpenSettingAutoSell}
        createOrder={createOrder}
        orderType={orderType}
        targetType={targetType}
        targetPrice={targetPrice}
        targetMC={targetMC}
        setPercent={setPercent}
        amount={amount}
        onShowSettings={onShowSettings}
        tokenQuoteSelected={tokenQuoteSelected}
        amountOrders={amountOrders}
        amountTime={amountTime}
        resolution={resolution}
        useAggregator={useAggregator}
        setUseAggregator={setUseAggregator}
      />

      <ModalAutoSell
        pair={pair}
        onClose={() => setIsShowSettingAutoSell(false)}
        isOpen={isShowSettingAutoSell}
        setReceiveToken={setReceiveToken}
        receiveToken={receiveToken}
        setTriggersOrder={setTriggersOrder}
        triggersOrder={triggersOrder}
        isOrder
      />
    </div>
  );
};
