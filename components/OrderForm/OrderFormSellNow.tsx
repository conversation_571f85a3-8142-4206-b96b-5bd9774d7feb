import BigNumber from "bignumber.js";
import { debounce, isEmpty } from "lodash";
import * as React from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { WalletIcon } from "@/assets/icons";
import { AppNumber, AppSymbolToken } from "@/components";
import { OrderFormType, OrderLimitTargetType } from "@/enums";
import { useOrder, useUser, useAggregatorAutoToggle } from "@/hooks";
import Storage from "@/libs/storage";
import { toastError } from "@/libs/toast";
import { RootState, AppDispatch } from "@/store";
import {
  setIsShowModalAddWallet,
  setIsShowModalEnableTradingPrivy,
} from "@/store/metadata.store";
import { TPair, TPairPrice } from "@/types";
import { abs, dividedBN, multipliedBN, toStringBN } from "@/utils/helper";
import { getCirculatingSupply } from "@/utils/pair";
import { AmountForm } from "./sell-order/AmountForm";
import { TargetForm } from "./sell-order/TargetForm";
import { FooterForm } from "./sell-order/FooterForm";
import { useMediaQuery } from "react-responsive";
import { SelectSellTypeMobile } from "./sell-order/SelectSellTypeMobile";
import { OPTIONS_ORDER_TYPE } from "@/utils/contants";
import { DCAForm } from "./buy-order/DCAForm";
import { usePairPrice } from "@/hooks/usePairPrice";
import { AggregatorToggle } from "./AggregatorToggle";
import { usePrivyTradingEnablement } from "@/hooks/usePrivyTradingEnablement";

export const OrderFormSellNow = ({
  onShowSettings,
  onShowSelectWallet,
  totalBalanceTokenBase,
  setOrderType,
  orderType,
  pair,
}: {
  onShowSettings: () => void;
  onShowSelectWallet: () => void;
  setOrderType: (value: string) => void;
  totalBalanceTokenBase: string | number;
  orderType: any;
  pair: TPair;
}) => {
  const { pairPrice } = usePairPrice(pair);
  const userSettings = Storage.getUserSettings();
  // const [sellType, setSellType] = useState<any>(OrderFormType.MARKET);
  const [sellPercent, setSellPercent] = useState<any>("");

  // for Limit
  const [targetMC, setTargetMC] = useState<any>("");
  const [targetType, setTargetType] = useState<any>(
    userSettings.orderLimitTargetType || OrderLimitTargetType.MC
  );
  const [targetPrice, setTargetPrice] = useState<any>("");
  const tokenAmountEstimateRef = useRef<any>("");
  const [tokenAmountEstimate, setTokenAmountEstimate] = useState<any>("");

  // for DCA
  const [marketCapTo, setMarketCapTo] = useState<any>("");
  const [marketCapFrom, setMarketCapFrom] = useState<any>("");
  const [resolution, setResolution] = useState<string>("H");
  const [amountTime, setAmountTime] = useState<any>("");
  const [amountOrders, setAmountOrders] = useState<any>("");
  const [isSelectMCRange, setIsSelectMCRange] = useState<boolean>(false);
  const { useAggregator, setUseAggregator } = useAggregatorAutoToggle(
    pair?.createdAt
  );

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const { quickSell, sellLimit, sellDCA, estimateQuickSell } = useOrder();
  const { activeWallets, activeTotalSuiBalance } = useUser();
  const dispatch = useDispatch<AppDispatch>();
  const targetConvertPercent = useRef("1");

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const { isPrivyUser, isTradingEnabled } = usePrivyTradingEnablement();

  const onAddWallet = () => {
    dispatch(setIsShowModalAddWallet({ isShow: true }));
  };

  useEffect(() => {
    if (isEmpty(pair) || orderType !== OrderFormType.DCA) return;
    const marketCap = multipliedBN(
      pairPrice?.priceUsd,
      getCirculatingSupply(pair)
    );

    setMarketCapTo(multipliedBN(marketCap, 1.1));
    setMarketCapFrom(multipliedBN(marketCap, 0.9));
  }, [pair?.pairId, orderType]);

  useEffect(() => {
    if (isEmpty(pair)) return;
    setTargetMC(multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair)));
    setTargetPrice(toStringBN(pairPrice?.priceUsd));
    Storage.setUserSettings("orderLimitTargetType", targetType);
  }, [pair?.pairId, targetType]);

  useEffect(() => {
    setSellPercent("");
  }, [orderType]);

  useEffect(() => {
    if (+sellPercent === 100) {
      setAmountOrders(1);
    }
  }, [sellPercent]);

  const debouncedEstimateQuickSell = useCallback(
    debounce((pair, sellPercent) => {
      estimateQuickSell(pair, +sellPercent).then((totalAmountOut) => {
        if (totalAmountOut) {
          tokenAmountEstimateRef.current = multipliedBN(
            totalAmountOut,
            targetConvertPercent.current
          );
          setTokenAmountEstimate(tokenAmountEstimateRef.current);
        }
      });
    }, 1500),
    [sellPercent, pair?.pairId]
  );

  useEffect(() => {
    tokenAmountEstimateRef.current = "";
    setTokenAmountEstimate(tokenAmountEstimateRef.current);
    if (!accessToken || !sellPercent) return;
    debouncedEstimateQuickSell(pair, sellPercent);

    return () => {
      debouncedEstimateQuickSell.cancel();
    };
  }, [accessToken, sellPercent, targetPrice, targetMC]);

  useEffect(() => {
    targetConvertPercent.current = "1";
    if (!accessToken || orderType !== OrderFormType.LIMIT) return;
    let rate;
    if (targetType === OrderLimitTargetType.MC) {
      rate = dividedBN(
        targetMC,
        multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair))
      );
    } else if (targetType === OrderLimitTargetType.PRICE) {
      rate = dividedBN(targetPrice, pairPrice?.priceUsd);
    } else {
      return;
    }

    targetConvertPercent.current = rate;
    if (tokenAmountEstimateRef.current) {
      tokenAmountEstimateRef.current = multipliedBN(
        tokenAmountEstimateRef.current,
        rate
      );
      setTokenAmountEstimate(tokenAmountEstimateRef.current);
    }
  }, [accessToken, targetMC, targetPrice, targetType, orderType]);

  const onOrderSuccess = () => {
    setSellPercent("");
    setTokenAmountEstimate("");
    setAmountOrders("");
    setAmountTime("");
    setIsSelectMCRange(false);
    tokenAmountEstimateRef.current = "";
  };

  const createOrderSellMarket = async () => {
    await quickSell(pair, +sellPercent, useAggregator, () => {
      onOrderSuccess();
    });
  };

  const createOrderSellLimit = async () => {
    if (targetType === OrderLimitTargetType.MC) {
      const targetMcPercent = new BigNumber(
        dividedBN(
          targetMC,
          multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair))
        )
      )
        .minus(1)
        .multipliedBy(100)
        .toFixed(8, BigNumber.ROUND_DOWN);

      if (!+targetMcPercent || +abs(targetMcPercent) < 0.1) {
        toastError("Error", "MC target too small!");
        return;
      }

      const targetPriceUsd = dividedBN(targetMC, getCirculatingSupply(pair));

      await sellLimit(
        pair,
        +sellPercent,
        targetPriceUsd,
        +targetMcPercent,
        null,
        useAggregator,
        () => {
          onOrderSuccess();
        }
      );
      return;
    }

    const targetPricePercent = new BigNumber(
      dividedBN(targetPrice, pairPrice?.priceUsd)
    )
      .minus(1)
      .multipliedBy(100)
      .toFixed(8, BigNumber.ROUND_DOWN);

    if (!+targetPricePercent || +abs(targetPricePercent) < 0.1) {
      toastError("Error", "Price target too small!");
      return;
    }

    await sellLimit(
      pair,
      +sellPercent,
      toStringBN(targetPrice),
      null,
      +targetPricePercent,
      useAggregator,
      () => {
        onOrderSuccess();
      }
    );
  };

  const creatOrderSellDCA = async () => {
    let tokenPriceRange = {
      min: null,
      max: null,
    };

    if (isSelectMCRange) {
      let priceMin = null;
      let priceMax = null;

      if (!!marketCapFrom) {
        priceMin = dividedBN(marketCapFrom, getCirculatingSupply(pair));
      }

      if (!!marketCapTo) {
        priceMax = dividedBN(marketCapTo, getCirculatingSupply(pair));
      }

      tokenPriceRange = {
        min: priceMin,
        max: priceMax,
      } as any;
    }

    const getInterval = () => {
      if (!amountTime) return "";
      if (resolution === "D") return amountTime * 86400;
      if (resolution === "H") return amountTime * 3600;
      if (resolution === "M") return amountTime * 60;
      return "";
    };

    await sellDCA(
      pair,
      getInterval(),
      amountOrders,
      tokenPriceRange,
      +sellPercent,
      useAggregator,
      () => {
        onOrderSuccess();
      }
    ).then();
  };

  const createOrder = async () => {
    if (!+sellPercent || new BigNumber(sellPercent).isZero()) {
      toastError("Error", "Please input amount!");
      return;
    }

    if (!+sellPercent) {
      toastError("Error", "Amount too small!");
      return;
    }

    if (isPrivyUser && !isTradingEnabled) {
      dispatch(setIsShowModalEnableTradingPrivy({ isShow: true }));
      return;
    }

    if (orderType === OrderFormType.MARKET) {
      createOrderSellMarket().then();
      return;
    }

    if (orderType === OrderFormType.LIMIT) {
      createOrderSellLimit().then();
      return;
    }

    if (orderType === OrderFormType.DCA) {
      creatOrderSellDCA().then();
      return;
    }

    return;
  };

  const _renderHeaderForm = () => {
    if (isMobile) {
      const _renderWallet = () => {
        if (!!wallets.length) {
          return (
            <div
              onClick={onShowSelectWallet}
              className="bg-neutral-alpha-50 flex w-fit cursor-pointer items-center justify-between rounded-[4px] p-[4px]"
            >
              <div className="flex items-center gap-2">
                <div className=" flex h-6 w-6 items-center justify-center rounded-[4px]">
                  <WalletIcon />
                </div>

                <div className="body-xs-regular-10 text-brand-500 bg-brand-800 border-brand-800 rounded-[80px] border px-[6px]">
                  {activeWallets.length}
                </div>
              </div>
            </div>
          );
        }

        return (
          <div className="flex items-center gap-2">
            <div className="bg-neutral-alpha-50 flex items-center justify-between rounded-[4px] px-4 py-1">
              <div className="flex h-6 w-6 items-center justify-center rounded-[4px]">
                <WalletIcon />
              </div>
            </div>
            <div className="text-white-500 whitespace-nowrap text-[12px]">
              0 wallet
            </div>
          </div>
        );
      };

      return (
        <div className="flex gap-4">
          <div>{_renderWallet()}</div>

          <div className="flex w-full items-center justify-end gap-2">
            {!!wallets.length ? (
              <div className="flex items-center gap-2">
                <div className="body-sm-regular-12 text-white-500 border-white-50 flex border-r pr-2">
                  <AppNumber
                    value={activeTotalSuiBalance}
                    className="text-white-800 pr-1"
                  />
                  SUI
                </div>
                <div className="body-sm-regular-12 text-white-500 flex">
                  <AppNumber
                    value={totalBalanceTokenBase}
                    className="text-white-800 pr-1"
                  />
                  <AppSymbolToken
                    symbol={pair?.tokenBase?.symbol || "Unknown"}
                  />
                </div>
              </div>
            ) : (
              accessToken && (
                <div
                  className="text-brand-500 cursor-pointer text-[12px] font-medium"
                  onClick={onAddWallet}
                >
                  Add wallet
                </div>
              )
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="flex gap-4">
        <div className="flex w-full items-center justify-end gap-2">
          <div className="border-neutral-alpha-50 flex flex-1 gap-3 border-b pb-1">
            {OPTIONS_ORDER_TYPE.map((item, index) => {
              return (
                <div
                  key={index}
                  onClick={() => {
                    setOrderType(item.value);
                  }}
                  className={`mb-[-4px] cursor-pointer 
                   ${
                     orderType === item.value
                       ? "text-white-1000 border-neutral-alpha-500 body-sm-semibold-12 border-b"
                       : "text-white-500 action-xs-medium-12"
                   }`}
                >
                  {item.name}
                </div>
              );
            })}
          </div>

          {!!wallets.length ? (
            <div className="flex items-center gap-2">
              <div className="body-sm-regular-12 text-white-500 flex">
                <AppNumber
                  value={totalBalanceTokenBase}
                  className="text-white-800 pr-1"
                />
                <AppSymbolToken symbol={pair?.tokenBase?.symbol || "Unknown"} />
              </div>
            </div>
          ) : (
            accessToken && (
              <div
                className="text-brand-500 cursor-pointer text-[12px] font-medium"
                onClick={onAddWallet}
              >
                Add wallet
              </div>
            )
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      {_renderHeaderForm()}

      {isMobile && (
        <SelectSellTypeMobile
          pair={pair}
          sellType={orderType}
          setSellType={setOrderType}
          setTargetMC={setTargetMC}
          setTargetPrice={setTargetPrice}
          setTargetType={setTargetType}
          targetMC={targetMC}
          targetType={targetType}
          targetPrice={targetPrice}
        />
      )}

      <AmountForm
        pair={pair}
        sellPercent={sellPercent}
        setSellPercent={setSellPercent}
        sellType={orderType}
      />

      {orderType === OrderFormType.LIMIT && !isMobile && (
        <div className="border-neutral-alpha-50 mt-6 overflow-hidden rounded-[8px] border">
          <TargetForm
            pair={pair}
            targetType={targetType}
            setTargetType={setTargetType}
            targetMC={targetMC}
            setTargetMC={setTargetMC}
            targetPrice={targetPrice}
            setTargetPrice={setTargetPrice}
          />
        </div>
      )}

      {orderType === OrderFormType.DCA && (
        <DCAForm
          pair={pair}
          isSelectMCRange={isSelectMCRange}
          marketCapTo={marketCapTo}
          marketCapFrom={marketCapFrom}
          resolution={resolution}
          amountOrders={amountOrders}
          amountTime={amountTime}
          setMarketCapTo={setMarketCapTo}
          setMarketCapFrom={setMarketCapFrom}
          setResolution={setResolution}
          setAmountOrders={setAmountOrders}
          setAmountTime={setAmountTime}
          setIsSelectMCRange={setIsSelectMCRange}
          sellPercent={sellPercent}
        />
      )}

      <FooterForm
        pair={pair}
        sellType={orderType}
        tokenAmountEstimate={tokenAmountEstimate}
        sellPercent={sellPercent}
        onShowSettings={onShowSettings}
        createOrder={createOrder}
        totalBalanceTokenBase={totalBalanceTokenBase}
        amountOrders={amountOrders}
        amountTime={amountTime}
        resolution={resolution}
        useAggregator={useAggregator}
        setUseAggregator={setUseAggregator}
      />
    </div>
  );
};
