import { BaseModal } from "@/modals/BaseModal";
import React from "react";
import { AppButton } from "@/components";
import { formatShortAddress } from "@/utils/format";

export const PrivySessionSignerModal = ({
  isOpen,
  onClose,
  onConfirm,
  walletAddress,
  isEnabling = true,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  walletAddress: string;
  isEnabling?: boolean;
}) => {
  const handleConfirm = async () => {
    try {
      await onConfirm();
    } catch (e) {
      console.error(e);
    } finally {
      onClose();
    }
  };

  const title = isEnabling ? "Enable Trading" : "Disable Trading";
  const description = isEnabling
    ? "Are you sure you want to authorize RaidenX to create and execute transactions on your wallet?"
    : "Are you sure you want to disable trading for this wallet?";

  const actionText = isEnabling ? "Enable" : "Disable";
  const buttonVariant = isEnabling ? "buy" : "sell";

  return (
    <BaseModal
      className="w-[calc(100vw-16px)] max-w-[420px]"
      isOpen={isOpen}
      title={title}
      onClose={onClose}
      description={description}
      headerClassName="mt-[12px]"
    >
      <div className="mb-[20px] mt-[16px]">
        <div className="text-white-700 body-sm-regular-12 text-center">
          <span className="text-white-500">Wallet: </span>
          <span className="text-white-900 font-medium">
            {formatShortAddress(walletAddress, 6, 4)}
          </span>
        </div>
      </div>

      <div className="flex justify-center gap-[8px]">
        <AppButton size="large" onClick={onClose} variant="outline">
          Cancel
        </AppButton>
        <AppButton size="large" onClick={handleConfirm} variant={buttonVariant}>
          {actionText}
        </AppButton>
      </div>
    </BaseModal>
  );
};
