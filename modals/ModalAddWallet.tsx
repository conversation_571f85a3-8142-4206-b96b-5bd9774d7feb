"use client";

import React, { useState } from "react";
import AppInput from "@/components/AppInput";
import { AppButton } from "@/components";
import rf from "@/services/RequestFactory";
import { toastError, toastSuccess } from "@/libs/toast";
import { isValidSuiAddress } from "@/utils/helper";
import { TGroup } from "../types/wallet-tracker";
import AppModal from "./AppModal";
import EmojiPicker, { Theme } from "emoji-picker-react";
import { AppPopover } from "@/components/AppPopover";

const SelectEmoji = ({
  emoji,
  setEmoji,
}: {
  emoji: string;
  setEmoji: (value: string) => void;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  return (
    <AppPopover
      position="top"
      trigger={
        <div className="border-white-100 cursor-pointer rounded-[6px] border p-2">
          {emoji}
        </div>
      }
      content={
        <EmojiPicker
          theme={Theme.DARK}
          onEmojiClick={(emoji) => {
            setEmoji(emoji.emoji);
            setIsOpen(false);
          }}
        />
      }
      isOpen={isOpen}
      onToggle={() => setIsOpen(!isOpen)}
      onClose={() => setIsOpen(false)}
    />
  );
};

const ModalAddWallet = ({
  onClose,
  group,
  fetchGroups,
  isOpen,
  onFetchData,
}: {
  isOpen: boolean;
  group?: TGroup;
  onClose: () => void;
  fetchGroups: () => void;
  onFetchData: () => void;
}) => {
  const [walletName, setWalletName] = useState<string>("");
  const [walletAddress, setWalletAddress] = useState<string>("");
  const [emoji, setEmoji] = useState<string>("👻");

  const addWallets = async () => {
    try {
      let dataGroup = group;
      if (!dataGroup) {
        dataGroup = await rf.getRequest("WalletTrackerRequest").addGroup({
          color: "#00FFCC",
          description: "Group Main",
          name: "main",
        });
      }
      if (dataGroup) {
        await rf.getRequest("WalletTrackerRequest").addWallet({
          groupId: dataGroup?.id,
          walletAddress,
          walletName,
          emoji,
        });
        toastSuccess("Success", "Add Successfully!");
        onClose();
        fetchGroups();
        onFetchData();
      }
    } catch (e: any) {
      toastError("Error", e?.message || "Something went wrong!");
      console.log(e);
    }
  };

  return (
    <AppModal
      isOpen={isOpen}
      title="Add Wallet"
      onClose={onClose}
      className="w-[343px] max-w-[375px]"
    >
      <div className="mt-[22px] gap-4">
        <div className="mb-4">
          <AppInput
            value={walletAddress}
            onChange={(e: any) => setWalletAddress(e.target.value?.trim())}
            label="Wallet Address"
            placeholder="Enter here"
            rootClassName="!h-[34px] !bg-transparent"
            labelClassName="text-white-700 action-sm-medium-12"
            className="placeholder:text-white-300 body-sm-medium-12"
          />
          {walletAddress && !isValidSuiAddress(walletAddress) && (
            <div className="body-xs-regular-10 text-red-600">
              Invalid wallet
            </div>
          )}
        </div>

        <div>
          <div className="body-sm-medium-12 text-white-700 mb-2">
            Wallet Name
          </div>
          <div className="flex gap-2">
            <SelectEmoji emoji={emoji} setEmoji={setEmoji} />
            <div className="flex-1">
              <AppInput
                value={walletName}
                onChange={(e: any) => setWalletName(e.target.value)}
                placeholder="Enter here"
                rootClassName="!h-[34px] mb-8 !bg-transparent"
                labelClassName="text-white-700 action-sm-medium-12"
                className="placeholder:text-white-300 body-sm-medium-12"
              />
            </div>
          </div>
        </div>

        <AppButton
          disabled={
            !walletAddress || !walletName || !isValidSuiAddress(walletAddress)
          }
          onClick={addWallets}
          variant="buy"
          size="large"
        >
          Add Wallet
        </AppButton>
      </div>
    </AppModal>
  );
};

export default ModalAddWallet;
