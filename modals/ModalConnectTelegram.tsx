import React from "react";
import { AppButton } from "@/components";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import {
  setIsShowModalConnectTelegram,
  setIsShowModalEnterCode,
} from "@/store/metadata.store";
import { BaseModal } from "@/modals/BaseModal";
import Storage from "@/libs/storage";
import config from "@/config";
import { usePrivyLogin } from "@/hooks";

export const ModalConnectTelegram = () => {
  const isOpen = useSelector(
    (state: RootState) => state.metadata.isShowModalConnectTelegram
  );
  const dispatch = useDispatch<AppDispatch>();
  const { onPrivyLogin } = usePrivyLogin();

  const onClose = () => {
    dispatch(setIsShowModalConnectTelegram({ isShow: false }));
  };

  const onShowModalEnterCode = () => {
    dispatch(setIsShowModalEnterCode({ isShow: true }));
    onClose();
  };

  const getLoginUrl = () => {
    const referralCode = Storage.getReferralCode();
    if (referralCode) {
      return `${config.link_telegram}?start=${referralCode}`;
    }
    return `${config.link_telegram}?start=login`;
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      closable={false}
      title="Get Login Code"
      description="Connect your Telegram account to receive a secure login code and proceed with logging in"
      descClassName="max-w-[362px] mt-2"
      className="w-[calc(100vw-32px)] max-w-[394px]"
    >
      <div className="flex w-full flex-col items-center gap-[14px]">
        <a
          href={getLoginUrl()}
          target="_blank"
          className="block w-full"
          onClick={onShowModalEnterCode}
        >
          <AppButton className="w-full" variant="buy" size="large">
            Connect Telegram
          </AppButton>
        </a>
        <AppButton
          onClick={onPrivyLogin}
          className="mt-4 w-full max-w-[388px] items-center gap-2"
          size="large"
        >
          Login with email or socials
        </AppButton>

        <div className="body-md-regular-14 text-white-500">Or</div>
        <div className="body-md-regular-14">
          Already have code?{" "}
          <span
            className="text-brand-500 cursor-pointer"
            onClick={onShowModalEnterCode}
          >
            Log In
          </span>
        </div>
      </div>
    </BaseModal>
  );
};
