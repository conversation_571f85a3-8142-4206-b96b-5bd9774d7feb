"use client";

import React from "react";
import rf from "@/services/RequestFactory";
import { toastError, toastSuccess } from "@/libs/toast";
import { TWalletTracker } from "../types/wallet-tracker";
import AppModal from "./AppModal";
import EmojiPicker, { Theme } from "emoji-picker-react";

const ModalEditEmojiWallet = ({
  onClose,
  wallet,
  isOpen,
  onFetchData,
}: {
  isOpen: boolean;
  wallet: TWalletTracker;
  onClose: () => void;
  onFetchData: () => void;
}) => {
  const editEmojiWallet = async (emoji: string) => {
    try {
      await rf
        .getRequest("WalletTrackerRequest")
        .editWallet(wallet.id, { emoji: emoji });
      toastSuccess("Success", "Edit Successfully!");
      onFetchData();
      onClose();
    } catch (e: any) {
      toastError("Error", e?.message || "Something went wrong!");
      console.log(e);
    }
  };

  return (
    <AppModal
      isOpen={isOpen}
      title="Select Emoji"
      onClose={onClose}
      className="!min-w-max max-w-[375px]"
    >
      <div>
        <EmojiPicker
          theme={Theme.DARK}
          onEmojiClick={(emoji) => {
            editEmojiWallet(emoji.emoji).then();
          }}
        />
      </div>
    </AppModal>
  );
};

export default ModalEditEmojiWallet;
