import { TPair } from "./pair.type";

export type TWalletTracker = {
  createdAt: number;
  group: TGroupWallet;
  id: string;
  updatedAt: number;
  walletAddress: string;
  walletName: string;
  soundNotification: string;
  emoji: string;
};

export type TGroupWallet = {
  id: string;
  name: string;
  color: string;
};

export type TTransaction = {
  amount: string;
  marketCap: string;
  tokenSymbol: string;
  tradingType: string;
  walletAddress: string;
  walletName: string;
  pair: TPair;
  avgBuy: string;
  avgSell: string;
  imageToken: string;
  iconUrl?: string;
  status: string;
  timestamp: number;
  hash: string;
  price: string;
  priceUsd: string;
};

export type TGroup = {
  color: string;
  createdAt: number;
  description: string;
  id: string;
  name: string;
  updatedAt: number;
  walletsCount: number;
};
